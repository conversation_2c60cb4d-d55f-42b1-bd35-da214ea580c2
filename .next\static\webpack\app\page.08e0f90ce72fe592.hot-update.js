"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Delete.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Warning.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ModelSelector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/ModelSelector */ \"(app-pages-browser)/./app/components/ModelSelector.tsx\");\n/* harmony import */ var _components_MCPServerManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/MCPServerManager */ \"(app-pages-browser)/./app/components/MCPServerManager.tsx\");\n/* harmony import */ var _components_ChatWindow_ChatWindow__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/ChatWindow/ChatWindow */ \"(app-pages-browser)/./app/components/ChatWindow/ChatWindow.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    var _getActiveConversation, _getActiveConversation1;\n    _s();\n    const theme = (0,_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(theme.breakpoints.down('md'));\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!isMobile);\n    const [mcpConfig, setMcpConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mcpServers: {}\n    });\n    // Save MCP config when it changes\n    const updateMcpConfig = async (newConfig)=>{\n        setMcpConfig(newConfig);\n        try {\n            const response = await fetch('/api/mcp', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'update_config',\n                    config: newConfig\n                })\n            });\n            if (!response.ok) {\n                console.error('Failed to save MCP config');\n            }\n        } catch (error) {\n            console.error('Error saving MCP config:', error);\n        }\n    };\n    const [availableTools, setAvailableTools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeConversationId, setActiveConversationId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modelCapabilities, setModelCapabilities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            loadMCPConfig();\n            loadConversations();\n        }\n    }[\"HomePage.useEffect\"], []);\n    // Reload MCP config when servers are updated (only when needed)\n    const reloadMCPConfig = ()=>{\n        loadMCPConfig();\n    };\n    // Auto-create a new chat if none exists\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            if (conversations.length === 0 && !activeConversationId) {\n                createNewConversation();\n            }\n        }\n    }[\"HomePage.useEffect\"], [\n        conversations.length,\n        activeConversationId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            setSidebarOpen(!isMobile);\n        }\n    }[\"HomePage.useEffect\"], [\n        isMobile\n    ]);\n    const loadMCPConfig = async ()=>{\n        try {\n            console.log('🔍 Loading MCP config from /api/mcp...');\n            const response = await fetch('/api/mcp');\n            if (response.ok) {\n                const data = await response.json();\n                console.log('✅ Loaded MCP data:', data);\n                console.log('✅ Available tools from API:', data.tools);\n                setMcpConfig({\n                    mcpServers: data.mcpServers || {}\n                });\n                setAvailableTools(data.tools || []);\n                console.log('✅ Set available tools state:', data.tools || []);\n            } else {\n                console.error('❌ Failed to load MCP config:', response.status);\n            }\n        } catch (error) {\n            console.error('❌ Error loading MCP config:', error);\n        }\n    };\n    const loadConversations = ()=>{\n        // Load conversations from localStorage\n        const saved = localStorage.getItem('ollama-chat-conversations');\n        if (saved) {\n            try {\n                const parsed = JSON.parse(saved);\n                setConversations(parsed.map((conv)=>({\n                        ...conv,\n                        timestamp: new Date(conv.timestamp),\n                        messages: conv.messages.map((msg)=>({\n                                ...msg,\n                                timestamp: new Date(msg.timestamp)\n                            }))\n                    })));\n            } catch (error) {\n                console.error('Error loading conversations:', error);\n            }\n        }\n    };\n    const saveConversations = (convs)=>{\n        localStorage.setItem('ollama-chat-conversations', JSON.stringify(convs));\n    };\n    const createNewConversation = ()=>{\n        const newConversation = {\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n            title: 'New Conversation',\n            lastMessage: '',\n            timestamp: new Date(),\n            messageCount: 0,\n            messages: []\n        };\n        const updatedConversations = [\n            newConversation,\n            ...conversations\n        ];\n        setConversations(updatedConversations);\n        setActiveConversationId(newConversation.id);\n        saveConversations(updatedConversations);\n        if (isMobile) {\n            setSidebarOpen(false);\n        }\n    };\n    const selectConversation = (id)=>{\n        setActiveConversationId(id);\n        if (isMobile) {\n            setSidebarOpen(false);\n        }\n    };\n    const deleteConversation = (id)=>{\n        const updatedConversations = conversations.filter((conv)=>conv.id !== id);\n        setConversations(updatedConversations);\n        saveConversations(updatedConversations);\n        if (activeConversationId === id) {\n            setActiveConversationId(updatedConversations.length > 0 ? updatedConversations[0].id : null);\n        }\n    };\n    const handleDeleteCurrentChat = ()=>{\n        if (activeConversationId) {\n            deleteConversation(activeConversationId);\n            setDeleteDialogOpen(false);\n        }\n    };\n    const updateConversationWithMessage = (message)=>{\n        const activeConv = conversations.find((conv)=>conv.id === activeConversationId);\n        if (!activeConv) return;\n        const updatedConversation = {\n            ...activeConv,\n            messages: [\n                ...activeConv.messages,\n                message\n            ],\n            lastMessage: message.content,\n            timestamp: new Date(),\n            messageCount: activeConv.messageCount + 1,\n            title: activeConv.title === 'New Conversation' && message.role === 'user' ? message.content.substring(0, 50) + (message.content.length > 50 ? '...' : '') : activeConv.title\n        };\n        const updatedConversations = conversations.map((conv)=>conv.id === activeConversationId ? updatedConversation : conv);\n        setConversations(updatedConversations);\n        saveConversations(updatedConversations);\n    };\n    const getActiveConversation = ()=>{\n        return conversations.find((conv)=>conv.id === activeConversationId) || null;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n        initial: {\n            opacity: 0\n        },\n        animate: {\n            opacity: 1\n        },\n        transition: {\n            duration: 0.5\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            sx: {\n                height: '100vh',\n                display: 'flex',\n                overflow: 'hidden'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    sx: {\n                        width: sidebarOpen ? 280 : 0,\n                        transition: 'width 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                        overflow: 'hidden',\n                        borderRight: sidebarOpen ? '1px solid rgba(255, 255, 255, 0.08)' : 'none'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        sx: {\n                            width: 280,\n                            height: '100%',\n                            display: 'flex',\n                            flexDirection: 'column',\n                            backdropFilter: 'blur(20px)',\n                            background: 'rgba(255, 255, 255, 0.02)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                sx: {\n                                    p: 2,\n                                    borderBottom: '1px solid rgba(255, 255, 255, 0.05)'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        sx: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                sx: {\n                                                    flex: 1\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModelSelector__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    selectedModel: selectedModel,\n                                                    onModelSelect: setSelectedModel,\n                                                    onModelCapabilitiesChange: setModelCapabilities\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MCPServerManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                config: mcpConfig,\n                                                onConfigUpdate: updateMcpConfig,\n                                                availableTools: availableTools\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        sx: {\n                                            mt: 1.5,\n                                            display: 'flex',\n                                            gap: 1,\n                                            alignItems: 'center'\n                                        },\n                                        children: [\n                                            availableTools.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                sx: {\n                                                    px: 1.5,\n                                                    py: 0.5,\n                                                    borderRadius: 1,\n                                                    background: 'rgba(76, 175, 80, 0.1)',\n                                                    fontSize: '0.7rem',\n                                                    color: 'rgba(255, 255, 255, 0.8)',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: 0.5\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        sx: {\n                                                            width: 4,\n                                                            height: 4,\n                                                            borderRadius: '50%',\n                                                            bgcolor: '#4caf50'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    availableTools.length\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            Object.keys(mcpConfig.mcpServers || {}).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                sx: {\n                                                    px: 1.5,\n                                                    py: 0.5,\n                                                    borderRadius: 1,\n                                                    background: 'rgba(33, 150, 243, 0.1)',\n                                                    fontSize: '0.7rem',\n                                                    color: 'rgba(255, 255, 255, 0.8)',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: 0.5\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        sx: {\n                                                            width: 4,\n                                                            height: 4,\n                                                            borderRadius: '50%',\n                                                            bgcolor: '#2196f3'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    Object.keys(mcpConfig.mcpServers).length\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                sx: {\n                                    flex: 1,\n                                    overflow: 'auto',\n                                    p: 1.5\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        sx: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            justifyContent: 'space-between',\n                                            mb: 1.5\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                variant: \"caption\",\n                                                color: \"text.secondary\",\n                                                sx: {\n                                                    fontSize: '0.75rem',\n                                                    fontWeight: 500\n                                                },\n                                                children: \"Conversations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                onClick: createNewConversation,\n                                                sx: {\n                                                    p: 0.5,\n                                                    color: 'rgba(255, 255, 255, 0.6)',\n                                                    '&:hover': {\n                                                        color: 'rgba(255, 255, 255, 0.8)',\n                                                        background: 'rgba(255, 255, 255, 0.05)'\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    sx: {\n                                                        fontSize: '1rem',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: \"+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 13\n                                    }, this),\n                                    conversations.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        variant: \"body2\",\n                                        color: \"text.secondary\",\n                                        sx: {\n                                            textAlign: 'center',\n                                            py: 3,\n                                            fontSize: '0.8rem'\n                                        },\n                                        children: \"No conversations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, this) : conversations.map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            sx: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                p: 1.5,\n                                                mb: 0.5,\n                                                borderRadius: 1.5,\n                                                background: conversation.id === activeConversationId ? 'rgba(255, 255, 255, 0.08)' : 'transparent',\n                                                transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',\n                                                '&:hover': {\n                                                    background: conversation.id === activeConversationId ? 'rgba(255, 255, 255, 0.12)' : 'rgba(255, 255, 255, 0.04)',\n                                                    '& .delete-btn': {\n                                                        opacity: 1\n                                                    }\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    onClick: ()=>selectConversation(conversation.id),\n                                                    sx: {\n                                                        flex: 1,\n                                                        cursor: 'pointer'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            variant: \"body2\",\n                                                            fontWeight: 500,\n                                                            noWrap: true,\n                                                            sx: {\n                                                                fontSize: '0.85rem'\n                                                            },\n                                                            children: conversation.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            variant: \"caption\",\n                                                            color: \"text.secondary\",\n                                                            noWrap: true,\n                                                            sx: {\n                                                                fontSize: '0.7rem'\n                                                            },\n                                                            children: [\n                                                                conversation.messageCount,\n                                                                \" messages\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 19\n                                                }, this),\n                                                conversation.id === activeConversationId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"delete-btn\",\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        setDeleteDialogOpen(true);\n                                                    },\n                                                    sx: {\n                                                        p: 0.5,\n                                                        opacity: 0,\n                                                        transition: 'all 0.2s ease',\n                                                        color: 'rgba(255, 255, 255, 0.4)',\n                                                        '&:hover': {\n                                                            color: '#ff6b6b',\n                                                            background: 'rgba(255, 107, 107, 0.1)'\n                                                        }\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        sx: {\n                                                            fontSize: '0.9rem'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, conversation.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    sx: {\n                        flex: 1,\n                        display: 'flex',\n                        flexDirection: 'column'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            sx: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                px: 2,\n                                py: 1.5,\n                                borderBottom: '1px solid rgba(255, 255, 255, 0.05)',\n                                background: 'rgba(0, 0, 0, 0.1)'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                    sx: {\n                                        mr: 2,\n                                        p: 0.5,\n                                        color: 'rgba(255, 255, 255, 0.7)',\n                                        '&:hover': {\n                                            background: 'rgba(255, 255, 255, 0.05)'\n                                        }\n                                    },\n                                    children: \"☰\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"subtitle1\",\n                                    sx: {\n                                        flex: 1,\n                                        fontSize: '0.9rem',\n                                        fontWeight: 500\n                                    },\n                                    children: ((_getActiveConversation = getActiveConversation()) === null || _getActiveConversation === void 0 ? void 0 : _getActiveConversation.title) || 'Chat'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 11\n                                }, this),\n                                selectedModel && modelCapabilities && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    sx: {\n                                        display: 'flex',\n                                        gap: 0.5,\n                                        alignItems: 'center'\n                                    },\n                                    children: [\n                                        modelCapabilities.supportsTools && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            sx: {\n                                                px: 1,\n                                                py: 0.25,\n                                                borderRadius: 0.75,\n                                                background: 'rgba(76, 175, 80, 0.15)',\n                                                border: '1px solid rgba(76, 175, 80, 0.3)',\n                                                fontSize: '0.7rem',\n                                                color: '#81c784',\n                                                fontWeight: 500\n                                            },\n                                            children: \"Tools\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 17\n                                        }, this),\n                                        modelCapabilities.supportsVision && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            sx: {\n                                                px: 1,\n                                                py: 0.25,\n                                                borderRadius: 0.75,\n                                                background: 'rgba(156, 39, 176, 0.15)',\n                                                border: '1px solid rgba(156, 39, 176, 0.3)',\n                                                fontSize: '0.7rem',\n                                                color: '#ba68c8',\n                                                fontWeight: 500\n                                            },\n                                            children: \"Vision\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 17\n                                        }, this),\n                                        modelCapabilities.thinking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            sx: {\n                                                px: 1,\n                                                py: 0.25,\n                                                borderRadius: 0.75,\n                                                background: 'rgba(255, 152, 0, 0.15)',\n                                                border: '1px solid rgba(255, 152, 0, 0.3)',\n                                                fontSize: '0.7rem',\n                                                color: '#ffb74d',\n                                                fontWeight: 500\n                                            },\n                                            children: \"Thinking\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            sx: {\n                                flex: 1,\n                                overflow: 'hidden'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatWindow_ChatWindow__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                selectedModel: selectedModel,\n                                availableTools: availableTools,\n                                conversation: getActiveConversation(),\n                                onMessageSent: updateConversationWithMessage,\n                                modelCapabilities: modelCapabilities\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                    lineNumber: 383,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    open: deleteDialogOpen,\n                    onClose: ()=>setDeleteDialogOpen(false),\n                    slotProps: {\n                        paper: {\n                            sx: {\n                                backdropFilter: 'blur(20px)',\n                                background: 'rgba(255, 255, 255, 0.1)',\n                                border: '1px solid rgba(255, 255, 255, 0.2)',\n                                color: 'white'\n                            }\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            sx: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                color: 'white'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    sx: {\n                                        mr: 1,\n                                        color: '#ff4444'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 11\n                                }, this),\n                                \"Delete Chat\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                color: \"rgba(255, 255, 255, 0.8)\",\n                                children: [\n                                    'Are you sure you want to delete \"',\n                                    (_getActiveConversation1 = getActiveConversation()) === null || _getActiveConversation1 === void 0 ? void 0 : _getActiveConversation1.title,\n                                    '\"? This action cannot be undone and all messages in this conversation will be permanently lost.'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    onClick: ()=>setDeleteDialogOpen(false),\n                                    sx: {\n                                        color: 'rgba(255, 255, 255, 0.7)'\n                                    },\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    onClick: handleDeleteCurrentChat,\n                                    variant: \"contained\",\n                                    sx: {\n                                        bgcolor: '#ff4444',\n                                        '&:hover': {\n                                            bgcolor: '#ff3333'\n                                        }\n                                    },\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                    lineNumber: 475,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n            lineNumber: 215,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n        lineNumber: 210,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"i8BljwjraogOEx2cyOlNio3iKnA=\", false, function() {\n    return [\n        _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});