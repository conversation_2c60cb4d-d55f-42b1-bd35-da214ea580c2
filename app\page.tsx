'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  useTheme,
  useMediaQuery,
  Typography,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { v4 as uuidv4 } from 'uuid';
import { motion } from 'framer-motion';
import ModelSelector from './components/ModelSelector';
import MCPServerManager from './components/MCPServerManager';
import ChatWindow from './components/ChatWindow/ChatWindow';
import { MCPConfig, Tool, ChatMessage } from './types';

interface Conversation {
  id: string;
  title: string;
  lastMessage: string;
  timestamp: Date;
  messageCount: number;
  messages: ChatMessage[];
}

export default function HomePage() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [selectedModel, setSelectedModel] = useState<string | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);
  const [mcpConfig, setMcpConfig] = useState<MCPConfig>({ mcpServers: {} });

  // Save MCP config when it changes
  const updateMcpConfig = async (newConfig: MCPConfig) => {
    setMcpConfig(newConfig);

    try {
      const response = await fetch('/api/mcp', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'update_config',
          config: newConfig,
        }),
      });

      if (!response.ok) {
        console.error('Failed to save MCP config');
      }
    } catch (error) {
      console.error('Error saving MCP config:', error);
    }
  };
  const [availableTools, setAvailableTools] = useState<Tool[]>([]);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversationId, setActiveConversationId] = useState<string | null>(null);
  const [modelCapabilities, setModelCapabilities] = useState<any>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  useEffect(() => {
    loadMCPConfig();
    loadConversations();
  }, []);

  // Reload MCP config when servers are updated (only when needed)
  const reloadMCPConfig = () => {
    loadMCPConfig();
  };

  // Auto-create a new chat if none exists
  useEffect(() => {
    if (conversations.length === 0 && !activeConversationId) {
      createNewConversation();
    }
  }, [conversations.length, activeConversationId]);

  useEffect(() => {
    setSidebarOpen(!isMobile);
  }, [isMobile]);

  const loadMCPConfig = async () => {
    try {
      console.log('🔍 Loading MCP config from /api/mcp...');
      const response = await fetch('/api/mcp');

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Loaded MCP data:', data);
        console.log('✅ Available tools from API:', data.tools);
        setMcpConfig({ mcpServers: data.mcpServers || {} });
        setAvailableTools(data.tools || []);
        console.log('✅ Set available tools state:', data.tools || []);
      } else {
        console.error('❌ Failed to load MCP config:', response.status);
      }
    } catch (error) {
      console.error('❌ Error loading MCP config:', error);
    }
  };

  const loadConversations = () => {
    // Load conversations from localStorage
    const saved = localStorage.getItem('ollama-chat-conversations');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        setConversations(parsed.map((conv: any) => ({
          ...conv,
          timestamp: new Date(conv.timestamp),
          messages: conv.messages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp),
          })),
        })));
      } catch (error) {
        console.error('Error loading conversations:', error);
      }
    }
  };

  const saveConversations = (convs: Conversation[]) => {
    localStorage.setItem('ollama-chat-conversations', JSON.stringify(convs));
  };

  const createNewConversation = () => {
    const newConversation: Conversation = {
      id: uuidv4(),
      title: 'New Conversation',
      lastMessage: '',
      timestamp: new Date(),
      messageCount: 0,
      messages: [],
    };

    const updatedConversations = [newConversation, ...conversations];
    setConversations(updatedConversations);
    setActiveConversationId(newConversation.id);
    saveConversations(updatedConversations);

    if (isMobile) {
      setSidebarOpen(false);
    }
  };

  const selectConversation = (id: string) => {
    setActiveConversationId(id);
    if (isMobile) {
      setSidebarOpen(false);
    }
  };

  const deleteConversation = (id: string) => {
    const updatedConversations = conversations.filter(conv => conv.id !== id);
    setConversations(updatedConversations);
    saveConversations(updatedConversations);

    if (activeConversationId === id) {
      setActiveConversationId(updatedConversations.length > 0 ? updatedConversations[0].id : null);
    }
  };

  const handleDeleteCurrentChat = () => {
    if (activeConversationId) {
      deleteConversation(activeConversationId);
      setDeleteDialogOpen(false);
    }
  };



  const updateConversationWithMessage = (message: ChatMessage) => {
    const activeConv = conversations.find(conv => conv.id === activeConversationId);
    if (!activeConv) return;

    const updatedConversation = {
      ...activeConv,
      messages: [...activeConv.messages, message],
      lastMessage: message.content,
      timestamp: new Date(),
      messageCount: activeConv.messageCount + 1,
      title: activeConv.title === 'New Conversation' && message.role === 'user'
        ? message.content.substring(0, 50) + (message.content.length > 50 ? '...' : '')
        : activeConv.title,
    };

    const updatedConversations = conversations.map(conv =>
      conv.id === activeConversationId ? updatedConversation : conv
    );

    setConversations(updatedConversations);
    saveConversations(updatedConversations);
  };

  const getActiveConversation = () => {
    return conversations.find(conv => conv.id === activeConversationId) || null;
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <Box sx={{ height: '100vh', display: 'flex', overflow: 'hidden' }}>
      {/* Minimal Sidebar */}
      <Box
        sx={{
          width: sidebarOpen ? 280 : 0,
          transition: 'width 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          overflow: 'hidden',
          borderRight: sidebarOpen ? '1px solid rgba(255, 255, 255, 0.08)' : 'none',
        }}
      >
        <Box
          sx={{
            width: 280,
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            backdropFilter: 'blur(20px)',
            background: 'rgba(255, 255, 255, 0.02)',
          }}
        >
          {/* Minimal Header */}
          <Box sx={{ p: 2, borderBottom: '1px solid rgba(255, 255, 255, 0.05)' }}>
            {/* Model Selector with Settings */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box sx={{ flex: 1 }}>
                <ModelSelector
                  selectedModel={selectedModel}
                  onModelSelect={setSelectedModel}
                  onModelCapabilitiesChange={setModelCapabilities}
                />
              </Box>
              <MCPServerManager
                config={mcpConfig}
                onConfigUpdate={updateMcpConfig}
                availableTools={availableTools}
              />
            </Box>

            {/* Compact Status Indicators */}
            <Box sx={{ mt: 1.5, display: 'flex', gap: 1, alignItems: 'center' }}>
              {availableTools.length > 0 && (
                <Box sx={{
                  px: 1.5,
                  py: 0.5,
                  borderRadius: 1,
                  background: 'rgba(76, 175, 80, 0.1)',
                  fontSize: '0.7rem',
                  color: 'rgba(255, 255, 255, 0.8)',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5,
                }}>
                  <Box sx={{ width: 4, height: 4, borderRadius: '50%', bgcolor: '#4caf50' }} />
                  {availableTools.length}
                </Box>
              )}
              {Object.keys(mcpConfig.mcpServers || {}).length > 0 && (
                <Box sx={{
                  px: 1.5,
                  py: 0.5,
                  borderRadius: 1,
                  background: 'rgba(33, 150, 243, 0.1)',
                  fontSize: '0.7rem',
                  color: 'rgba(255, 255, 255, 0.8)',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5,
                }}>
                  <Box sx={{ width: 4, height: 4, borderRadius: '50%', bgcolor: '#2196f3' }} />
                  {Object.keys(mcpConfig.mcpServers).length}
                </Box>
              )}
            </Box>
          </Box>

          {/* Conversations */}
          <Box sx={{ flex: 1, overflow: 'auto', p: 1.5 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1.5 }}>
              <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem', fontWeight: 500 }}>
                Conversations
              </Typography>
              <IconButton
                onClick={createNewConversation}
                sx={{
                  p: 0.5,
                  color: 'rgba(255, 255, 255, 0.6)',
                  '&:hover': {
                    color: 'rgba(255, 255, 255, 0.8)',
                    background: 'rgba(255, 255, 255, 0.05)',
                  },
                }}
              >
                <Box sx={{ fontSize: '1rem', fontWeight: 'bold' }}>+</Box>
              </IconButton>
            </Box>

            {conversations.length === 0 ? (
              <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 3, fontSize: '0.8rem' }}>
                No conversations
              </Typography>
            ) : (
              conversations.map((conversation) => (
                <Box
                  key={conversation.id}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    p: 1.5,
                    mb: 0.5,
                    borderRadius: 1.5,
                    background: conversation.id === activeConversationId
                      ? 'rgba(255, 255, 255, 0.08)'
                      : 'transparent',
                    transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                    '&:hover': {
                      background: conversation.id === activeConversationId
                        ? 'rgba(255, 255, 255, 0.12)'
                        : 'rgba(255, 255, 255, 0.04)',
                      '& .delete-btn': {
                        opacity: 1,
                      },
                    },
                  }}
                >
                  <Box
                    onClick={() => selectConversation(conversation.id)}
                    sx={{
                      flex: 1,
                      cursor: 'pointer',
                    }}
                  >
                    <Typography variant="body2" fontWeight={500} noWrap sx={{ fontSize: '0.85rem' }}>
                      {conversation.title}
                    </Typography>
                    <Typography variant="caption" color="text.secondary" noWrap sx={{ fontSize: '0.7rem' }}>
                      {conversation.messageCount} messages
                    </Typography>
                  </Box>

                  {conversation.id === activeConversationId && (
                    <IconButton
                      className="delete-btn"
                      onClick={(e) => {
                        e.stopPropagation();
                        setDeleteDialogOpen(true);
                      }}
                      sx={{
                        p: 0.5,
                        opacity: 0,
                        transition: 'all 0.2s ease',
                        color: 'rgba(255, 255, 255, 0.4)',
                        '&:hover': {
                          color: '#ff6b6b',
                          background: 'rgba(255, 107, 107, 0.1)',
                        },
                      }}
                    >
                      <DeleteIcon sx={{ fontSize: '0.9rem' }} />
                    </IconButton>
                  )}
                </Box>
              ))
            )}
          </Box>
        </Box>
      </Box>

      {/* Main Chat Area */}
      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {/* Minimal Top Bar */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            px: 2,
            py: 1.5,
            borderBottom: '1px solid rgba(255, 255, 255, 0.05)',
            background: 'rgba(0, 0, 0, 0.1)',
          }}
        >
          <IconButton
            onClick={() => setSidebarOpen(!sidebarOpen)}
            sx={{
              mr: 2,
              p: 0.5,
              color: 'rgba(255, 255, 255, 0.7)',
              '&:hover': {
                background: 'rgba(255, 255, 255, 0.05)',
              },
            }}
          >
            ☰
          </IconButton>

          <Typography variant="subtitle1" sx={{ flex: 1, fontSize: '0.9rem', fontWeight: 500 }}>
            {getActiveConversation()?.title || 'Chat'}
          </Typography>

          {/* Model Capabilities Display */}
          {selectedModel && modelCapabilities && (
            <Box sx={{ display: 'flex', gap: 0.5, alignItems: 'center' }}>
              {modelCapabilities.supportsTools && (
                <Box sx={{
                  px: 1,
                  py: 0.25,
                  borderRadius: 0.75,
                  background: 'rgba(76, 175, 80, 0.15)',
                  border: '1px solid rgba(76, 175, 80, 0.3)',
                  fontSize: '0.7rem',
                  color: '#81c784',
                  fontWeight: 500,
                }}>
                  Tools
                </Box>
              )}
              {modelCapabilities.supportsVision && (
                <Box sx={{
                  px: 1,
                  py: 0.25,
                  borderRadius: 0.75,
                  background: 'rgba(156, 39, 176, 0.15)',
                  border: '1px solid rgba(156, 39, 176, 0.3)',
                  fontSize: '0.7rem',
                  color: '#ba68c8',
                  fontWeight: 500,
                }}>
                  Vision
                </Box>
              )}
              {modelCapabilities.thinking && (
                <Box sx={{
                  px: 1,
                  py: 0.25,
                  borderRadius: 0.75,
                  background: 'rgba(255, 152, 0, 0.15)',
                  border: '1px solid rgba(255, 152, 0, 0.3)',
                  fontSize: '0.7rem',
                  color: '#ffb74d',
                  fontWeight: 500,
                }}>
                  Thinking
                </Box>
              )}
            </Box>
          )}
        </Box>

        {/* Chat Window */}
        <Box sx={{ flex: 1, overflow: 'hidden' }}>
          <ChatWindow
            selectedModel={selectedModel}
            availableTools={availableTools}
            conversation={getActiveConversation()}
            onMessageSent={updateConversationWithMessage}
            modelCapabilities={modelCapabilities}
          />
        </Box>
      </Box>

      {/* Delete Chat Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        slotProps={{
          paper: {
            sx: {
              backdropFilter: 'blur(20px)',
              background: 'rgba(255, 255, 255, 0.1)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              color: 'white',
            },
          },
        }}
      >
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', color: 'white' }}>
          <WarningIcon sx={{ mr: 1, color: '#ff4444' }} />
          Delete Chat
        </DialogTitle>
        <DialogContent>
          <Typography color="rgba(255, 255, 255, 0.8)">
            Are you sure you want to delete "{getActiveConversation()?.title}"?
            This action cannot be undone and all messages in this conversation will be permanently lost.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setDeleteDialogOpen(false)}
            sx={{ color: 'rgba(255, 255, 255, 0.7)' }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleDeleteCurrentChat}
            variant="contained"
            sx={{
              bgcolor: '#ff4444',
              '&:hover': { bgcolor: '#ff3333' }
            }}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
    </motion.div>
  );
}
