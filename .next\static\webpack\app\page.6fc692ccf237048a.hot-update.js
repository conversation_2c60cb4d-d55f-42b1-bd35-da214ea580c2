"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/ChatWindow/ChatWindow.tsx":
/*!**************************************************!*\
  !*** ./app/components/ChatWindow/ChatWindow.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatWindow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _MessageBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MessageBubble */ \"(app-pages-browser)/./app/components/ChatWindow/MessageBubble.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./app/components/ChatWindow/InputBar.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../TypingIndicator */ \"(app-pages-browser)/./app/components/TypingIndicator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ChatWindow(param) {\n    let { selectedModel, availableTools, conversation, onMessageSent, modelCapabilities } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messages = (conversation === null || conversation === void 0 ? void 0 : conversation.messages) || [];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWindow.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatWindow.useEffect\"], [\n        messages\n    ]);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const handleSendMessage = async (content, attachments)=>{\n        if (!selectedModel) {\n            setError('Please select a model first');\n            return;\n        }\n        // Create user message\n        const userMessage = {\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n            role: 'user',\n            content,\n            timestamp: new Date(),\n            attachments\n        };\n        onMessageSent(userMessage);\n        setLoading(true);\n        setError(null);\n        try {\n            // Check if message has vision content\n            const hasVisionContent = attachments === null || attachments === void 0 ? void 0 : attachments.some((att)=>att.type === 'image');\n            // Validate vision capability\n            if (hasVisionContent && !(modelCapabilities === null || modelCapabilities === void 0 ? void 0 : modelCapabilities.supportsVision)) {\n                setError(\"Model \".concat(selectedModel, \" does not support vision/image processing. Please select a vision-capable model like llava or qwen2-vl.\"));\n                setLoading(false);\n                return;\n            }\n            // Prepare messages for API\n            const conversationMessages = [\n                ...messages,\n                userMessage\n            ];\n            // Only include tools if model supports them\n            const toolsToSend = (modelCapabilities === null || modelCapabilities === void 0 ? void 0 : modelCapabilities.supportsTools) ? availableTools : [];\n            console.log('🔍 ChatWindow - Available tools:', availableTools);\n            console.log('🔍 ChatWindow - Model capabilities:', modelCapabilities);\n            console.log('🔍 ChatWindow - Tools to send:', toolsToSend);\n            console.log('🔍 ChatWindow - availableTools.length:', availableTools.length);\n            // Send to Ollama API\n            const response = await fetch('/api/ollama', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: selectedModel,\n                    messages: conversationMessages.map((msg)=>({\n                            role: msg.role,\n                            content: msg.content,\n                            ...msg.toolCall && {\n                                tool_calls: [\n                                    msg.toolCall\n                                ]\n                            }\n                        })),\n                    tools: toolsToSend,\n                    hasVisionContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to get response from Ollama');\n            }\n            const data = await response.json();\n            const assistantMessage = data.message;\n            console.log('Received assistant message:', assistantMessage);\n            console.log('Model capabilities:', modelCapabilities);\n            // Create assistant message\n            const newAssistantMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                role: 'assistant',\n                content: assistantMessage.content,\n                timestamp: new Date()\n            };\n            // Check if there are tool calls (only if model supports tools)\n            if ((modelCapabilities === null || modelCapabilities === void 0 ? void 0 : modelCapabilities.supportsTools) && assistantMessage.tool_calls && assistantMessage.tool_calls.length > 0) {\n                console.log('Tool calls detected:', assistantMessage.tool_calls);\n                const toolCall = assistantMessage.tool_calls[0];\n                // Add an ID to the tool call if it doesn't have one\n                if (!toolCall.id) {\n                    toolCall.id = (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n                }\n                newAssistantMessage.toolCall = toolCall;\n                // Add the assistant message with tool call\n                onMessageSent(newAssistantMessage);\n                // Execute the tool call\n                await executeToolCall(toolCall, [\n                    ...messages,\n                    userMessage,\n                    newAssistantMessage\n                ]);\n            } else {\n                // Add the assistant message\n                onMessageSent(newAssistantMessage);\n            }\n        } catch (err) {\n            console.error('Error sending message:', err);\n            setError('Failed to send message. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const executeToolCall = async (toolCall, currentMessages)=>{\n        try {\n            console.log('Executing tool call:', toolCall);\n            // Parse tool arguments\n            let toolArgs;\n            try {\n                toolArgs = typeof toolCall.function.arguments === 'string' ? JSON.parse(toolCall.function.arguments) : toolCall.function.arguments;\n            } catch (parseError) {\n                console.error('Failed to parse tool arguments:', parseError);\n                toolArgs = toolCall.function.arguments;\n            }\n            console.log('Tool arguments:', toolArgs);\n            // Determine server name - use the serverName from the tool if available\n            let serverName = 'duckduckgo-mcp-server'; // default to our Smithery AI server\n            // Check if the tool has a serverName property\n            const toolWithServer = availableTools.find((tool)=>tool.function.name === toolCall.function.name);\n            if (toolWithServer && toolWithServer.serverName) {\n                serverName = toolWithServer.serverName;\n            } else {\n                // Fallback mapping for legacy tools\n                if (toolCall.function.name === 'get_weather') {\n                    serverName = 'weather';\n                } else if (toolCall.function.name === 'search_web') {\n                    serverName = 'duckduckgo-mcp-server';\n                }\n            }\n            console.log(\"Using server: \".concat(serverName, \" for tool: \").concat(toolCall.function.name));\n            // Execute tool via MCP API\n            const response = await fetch('/api/mcp', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'execute_tool',\n                    serverName: serverName,\n                    toolName: toolCall.function.name,\n                    arguments: toolArgs\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to execute tool');\n            }\n            const toolResult = await response.json();\n            // Create tool result message\n            const toolResultMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                role: 'tool',\n                content: JSON.stringify(toolResult.result, null, 2),\n                timestamp: new Date(),\n                toolResult: {\n                    toolCallId: toolCall.id,\n                    result: toolResult.result\n                }\n            };\n            onMessageSent(toolResultMessage);\n            // Send the tool result back to Ollama for final response\n            const finalResponse = await fetch('/api/ollama', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: selectedModel,\n                    messages: [\n                        ...currentMessages,\n                        toolResultMessage\n                    ].map((msg)=>({\n                            role: msg.role,\n                            content: msg.content,\n                            ...msg.toolCall && {\n                                tool_calls: [\n                                    msg.toolCall\n                                ]\n                            }\n                        })),\n                    tools: availableTools\n                })\n            });\n            if (finalResponse.ok) {\n                const finalData = await finalResponse.json();\n                const finalMessage = {\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                    role: 'assistant',\n                    content: finalData.message.content,\n                    timestamp: new Date()\n                };\n                onMessageSent(finalMessage);\n            }\n        } catch (err) {\n            console.error('Error executing tool call:', err);\n            // Add error message\n            const errorMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                role: 'assistant',\n                content: 'Sorry, I encountered an error while using the tool. Please try again.',\n                timestamp: new Date()\n            };\n            onMessageSent(errorMessage);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        display: \"flex\",\n        flexDirection: \"column\",\n        height: \"100%\",\n        position: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                children: error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        sx: {\n                            p: 2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            severity: \"error\",\n                            onClose: ()=>setError(null),\n                            sx: {\n                                borderRadius: 2,\n                                backdropFilter: 'blur(20px)',\n                                background: 'rgba(255, 68, 68, 0.1)',\n                                border: '1px solid rgba(255, 68, 68, 0.3)',\n                                color: '#ff4444',\n                                '& .MuiAlert-icon': {\n                                    color: '#ff4444'\n                                }\n                            },\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 284,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                flex: 1,\n                overflow: \"auto\",\n                sx: {\n                    px: 3,\n                    py: 2\n                },\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        style: {\n                            height: '100%'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            alignItems: \"center\",\n                            justifyContent: \"center\",\n                            height: \"100%\",\n                            textAlign: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"h6\",\n                                    sx: {\n                                        color: 'rgba(255, 255, 255, 0.8)',\n                                        mb: 1,\n                                        fontWeight: 500\n                                    },\n                                    children: conversation ? 'Start chatting' : 'Select a conversation'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                    lineNumber: 331,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"body2\",\n                                    sx: {\n                                        color: 'rgba(255, 255, 255, 0.5)',\n                                        maxWidth: 300,\n                                        fontSize: '0.85rem'\n                                    },\n                                    children: conversation ? 'Type a message to start' : 'Select or create a conversation'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 317,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                        children: messages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.05,\n                                    duration: 0.2\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    message: message,\n                                    isUser: message.role === 'user'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 17\n                                }, this)\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                lineNumber: 359,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 357,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                        children: loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 315,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    p: 2,\n                    borderTop: '1px solid rgba(255, 255, 255, 0.05)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    onSendMessage: handleSendMessage,\n                    disabled: !selectedModel || !conversation,\n                    loading: loading,\n                    modelCapabilities: modelCapabilities\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                    lineNumber: 393,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 392,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n        lineNumber: 281,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatWindow, \"EYjj5BViCHitUwPd0M3b8vWEXZg=\");\n_c = ChatWindow;\nvar _c;\n$RefreshReg$(_c, \"ChatWindow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/ChatWindow/ChatWindow.tsx\n"));

/***/ })

});