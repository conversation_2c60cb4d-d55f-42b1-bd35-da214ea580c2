import { NextRequest, NextResponse } from 'next/server';
import { OllamaModel, OllamaResponse, ChatMessage, ModelCapabilities } from '@/types';

const OLLAMA_BASE_URL = process.env.OLLAMA_BASE_URL || 'http://localhost:11434';

// Function to detect model capabilities based on real Ollama model data
function detectModelCapabilities(model: any): ModelCapabilities {
  const modelName = model.name.toLowerCase();
  const family = model.details?.family?.toLowerCase() || '';
  const template = model.details?.template?.toLowerCase() || '';
  const parameters = model.details?.parameters || '';

  console.log(`🔍 Analyzing capabilities for ${modelName}:`);
  console.log(`- Family: ${family}`);
  console.log(`- Template length: ${template.length} chars`);
  console.log(`- Parameters: ${parameters}`);
  console.log(`- Model details:`, model.details);

  // ACCURATE tool detection - only models that actually support function calling
  const supportsTools = (
    // Check template for actual tool/function calling patterns
    template.includes('function_call') ||
    template.includes('tool_call') ||
    template.includes('available_functions') ||
    template.includes('function>') ||
    template.includes('<tool>') ||
    template.includes('tools:') ||
    template.includes('call_function') ||
    // Check for specific tool-capable models (VERIFIED ONLY)
    modelName.includes('llama3.1') ||
    modelName.includes('llama3.2') ||
    modelName.includes('llama3.3') ||
    modelName.includes('qwen2.5') ||
    modelName.includes('qwen2') ||
    modelName.includes('qwen3') ||
    modelName.includes('mistral-nemo') ||
    (modelName.includes('mistral') && !modelName.includes('7b')) ||
    modelName.includes('firefunction') ||
    modelName.includes('hermes-3') ||
    modelName.includes('nous-hermes') ||
    modelName.includes('deepseek-v3') ||
    modelName.includes('granite3') ||
    // Check parameters for function calling indicators
    parameters.includes('function_call') ||
    parameters.includes('tool_call')
  );

  // ACCURATE vision detection - multimodal models that can process images
  const supportsVision = (
    // Check template for vision/image patterns
    template.includes('<image>') ||
    template.includes('[img]') ||
    template.includes('image_url') ||
    template.includes('vision') ||
    template.includes('multimodal') ||
    template.includes('image') ||
    // Check for specific vision models (VERIFIED LIST)
    modelName.includes('llava') ||
    modelName.includes('moondream') ||
    modelName.includes('minicpm-v') ||
    modelName.includes('qwen2-vl') ||
    modelName.includes('qwen-vl') ||
    modelName.includes('pixtral') ||
    modelName.includes('internvl') ||
    modelName.includes('cogvlm') ||
    modelName.includes('yi-vl') ||
    modelName.includes('gemma2-2b-it') ||
    modelName.includes('gemma2-9b-it') ||
    modelName.includes('gemma2-27b-it') ||
    modelName.includes('gemma3-4b') ||
    modelName.includes('gemma3-8b') ||
    modelName.includes('llama3.2-11b-vision') ||
    modelName.includes('llama3.2-90b-vision') ||
    // Check family for vision indicators
    family.includes('llava') ||
    family.includes('vision') ||
    family.includes('gemma2') ||
    family.includes('gemma3')
  );

  // ACCURATE code detection
  const supportsCode = (
    // Check for specific code models
    modelName.includes('codellama') ||
    modelName.includes('codegemma') ||
    modelName.includes('starcoder') ||
    modelName.includes('deepseek-coder') ||
    modelName.includes('codeqwen') ||
    modelName.includes('granite-code') ||
    family.includes('code')
  );

  // ACCURATE thinking detection - models with reasoning capabilities
  const thinking = (
    // Check template for thinking patterns
    template.includes('<think>') ||
    template.includes('<thinking>') ||
    template.includes('reasoning') ||
    // Check for specific reasoning models
    modelName.includes('qwen2.5') ||
    modelName.includes('qwen3') ||
    modelName.includes('deepseek-r1') ||
    modelName.includes('deepseek-v3') ||
    modelName.includes('o1') ||
    modelName.includes('reasoning') ||
    // Check parameters
    parameters.includes('thinking') ||
    parameters.includes('reasoning')
  );

  console.log(`✅ Capabilities: Tools=${supportsTools}, Vision=${supportsVision}, Code=${supportsCode}, Thinking=${thinking}`);

  return {
    supportsTools,
    supportsVision,
    supportsCode,
    thinking,
    contextLength: getContextLength(modelName, family),
  };
}

function getContextLength(modelName: string, family: string): number {
  // Common context lengths based on model families
  if (modelName.includes('128k') || family.includes('granite')) return 128000;
  if (modelName.includes('32k')) return 32000;
  if (modelName.includes('16k')) return 16000;
  if (modelName.includes('8k')) return 8000;
  if (family.includes('llama3')) return 8192;
  if (family.includes('qwen')) return 32768;
  if (family.includes('mistral')) return 32768;
  return 4096; // Default
}

// GET /api/ollama - Fetch available models with detailed capabilities
export async function GET() {
  try {
    const response = await fetch(`${OLLAMA_BASE_URL}/api/tags`);

    if (!response.ok) {
      throw new Error(`Ollama API error: ${response.status}`);
    }

    const data = await response.json();

    // Fetch detailed model information for each model
    const modelsWithDetails = await Promise.all(
      (data.models || []).map(async (model: any) => {
        try {
          // Get detailed model information
          const showResponse = await fetch(`${OLLAMA_BASE_URL}/api/show`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ name: model.name })
          });

          if (showResponse.ok) {
            const modelDetails = await showResponse.json();
            return {
              ...model,
              details: {
                ...model.details,
                ...modelDetails.details,
                template: modelDetails.template,
                parameters: modelDetails.parameters,
                modelfile: modelDetails.modelfile
              },
              capabilities: detectModelCapabilities({
                ...model,
                details: modelDetails.details,
                template: modelDetails.template,
                parameters: modelDetails.parameters
              })
            };
          }
        } catch (error) {
          console.error(`Error fetching details for ${model.name}:`, error);
        }

        // Fallback to basic detection if detailed fetch fails
        return {
          ...model,
          capabilities: detectModelCapabilities(model)
        };
      })
    );

    return NextResponse.json({ models: modelsWithDetails });
  } catch (error) {
    console.error('Error fetching Ollama models:', error);
    return NextResponse.json(
      { error: 'Failed to fetch models from Ollama' },
      { status: 500 }
    );
  }
}

// POST /api/ollama - Send chat message
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { model, messages, tools, hasVisionContent } = body;

    if (!model) {
      return NextResponse.json(
        { error: 'Model is required' },
        { status: 400 }
      );
    }

    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json(
        { error: 'Messages array is required' },
        { status: 400 }
      );
    }

    // Format messages for Ollama API
    const formattedMessages = messages.map((msg: ChatMessage) => ({
      role: msg.role,
      content: msg.content,
      ...(msg.toolCall && { tool_calls: [msg.toolCall] }),
    }));

    // Get model capabilities to determine what features to include
    const modelsResponse = await fetch(`${OLLAMA_BASE_URL}/api/tags`);
    let modelCapabilities: ModelCapabilities = { supportsTools: false, supportsVision: false, supportsCode: false, thinking: false };

    if (modelsResponse.ok) {
      const modelsData = await modelsResponse.json();
      const modelInfo = modelsData.models?.find((m: any) => m.name === model);
      if (modelInfo) {
        modelCapabilities = detectModelCapabilities(modelInfo);
      }
    }

    // Prepare the request payload
    const payload: any = {
      model,
      messages: formattedMessages,
      stream: false,
    };

    // Only include tools if the model supports them and tools are provided
    if (tools && tools.length > 0 && modelCapabilities.supportsTools) {
      payload.tools = tools;
      console.log(`Sending tools to ${model}:`, JSON.stringify(tools, null, 2));
      console.log(`Model capabilities:`, modelCapabilities);
    } else {
      console.log(`Not sending tools - Model: ${model}, Tools available: ${tools?.length || 0}, Supports tools: ${modelCapabilities.supportsTools}`);
    }

    // Validate vision content for non-vision models
    if (hasVisionContent && !modelCapabilities.supportsVision) {
      return NextResponse.json(
        { error: `Model ${model} does not support vision/image processing. Please use a vision-capable model like llava or qwen2-vl.` },
        { status: 400 }
      );
    }

    // Make request to Ollama
    const response = await fetch(`${OLLAMA_BASE_URL}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Ollama API error:', errorText);
      throw new Error(`Ollama API error: ${response.status}`);
    }

    const data: OllamaResponse = await response.json();

    // Log the response for debugging
    console.log('Ollama response:', JSON.stringify(data, null, 2));

    // Check if the model made tool calls
    if (data.message.tool_calls && data.message.tool_calls.length > 0) {
      console.log('Tool calls detected:', data.message.tool_calls);
    }

    // Return the response
    return NextResponse.json({
      message: data.message,
      model: data.model,
      created_at: data.created_at,
      done: data.done,
    });

  } catch (error) {
    console.error('Error in Ollama chat:', error);
    return NextResponse.json(
      { error: 'Failed to process chat request' },
      { status: 500 }
    );
  }
}
