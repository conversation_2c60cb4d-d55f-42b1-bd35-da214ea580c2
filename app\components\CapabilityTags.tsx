'use client';

import React from 'react';
import {
  <PERSON>,
  Chip,
  Stack,
  Tooltip,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Build as ToolsIcon,
  Visibility as VisionIcon,
  Psychology as ThinkingIcon,
  Code as CodeIcon,
  Memory as MemoryIcon,
  Speed as SpeedIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface CapabilityTagsProps {
  capabilities: {
    supportsTools?: boolean;
    supportsVision?: boolean;
    supportsCode?: boolean;
    contextLength?: number;
    thinking?: boolean;
    streaming?: boolean;
  };
  availableToolsCount?: number;
}

export default function CapabilityTags({ 
  capabilities, 
  availableToolsCount = 0 
}: CapabilityTagsProps) {
  const theme = useTheme();

  const tags = [
    {
      key: 'tools',
      show: capabilities.supportsTools && availableToolsCount > 0,
      icon: <ToolsIcon fontSize="small" />,
      label: `Tools (${availableToolsCount})`,
      color: 'primary' as const,
      tooltip: `${availableToolsCount} AI tools available for enhanced capabilities`,
    },
    {
      key: 'vision',
      show: capabilities.supportsVision,
      icon: <VisionIcon fontSize="small" />,
      label: 'Vision',
      color: 'secondary' as const,
      tooltip: 'Can analyze and understand images',
    },
    {
      key: 'thinking',
      show: capabilities.thinking,
      icon: <ThinkingIcon fontSize="small" />,
      label: 'Thinking',
      color: 'info' as const,
      tooltip: 'Advanced reasoning and step-by-step thinking',
    },
    {
      key: 'code',
      show: capabilities.supportsCode,
      icon: <CodeIcon fontSize="small" />,
      label: 'Code',
      color: 'success' as const,
      tooltip: 'Specialized for code generation and analysis',
    },
    {
      key: 'memory',
      show: capabilities.contextLength && capabilities.contextLength > 8192,
      icon: <MemoryIcon fontSize="small" />,
      label: `${Math.round((capabilities.contextLength || 0) / 1024)}K`,
      color: 'warning' as const,
      tooltip: `Context length: ${capabilities.contextLength?.toLocaleString()} tokens`,
    },
    {
      key: 'streaming',
      show: capabilities.streaming,
      icon: <SpeedIcon fontSize="small" />,
      label: 'Stream',
      color: 'info' as const,
      tooltip: 'Real-time streaming responses',
    },
  ];

  const visibleTags = tags.filter(tag => tag.show);

  if (visibleTags.length === 0) {
    return null;
  }

  return (
    <Box
      sx={{
        position: 'absolute',
        top: 16,
        right: 16,
        zIndex: 10,
      }}
    >
      <Stack direction="row" spacing={1} flexWrap="wrap" justifyContent="flex-end">
        {visibleTags.map((tag, index) => (
          <motion.div
            key={tag.key}
            initial={{ opacity: 0, scale: 0.8, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            transition={{ 
              duration: 0.3, 
              delay: index * 0.1,
              type: "spring",
              stiffness: 300,
              damping: 20
            }}
          >
            <Tooltip title={tag.tooltip} arrow placement="bottom">
              <Chip
                icon={tag.icon}
                label={tag.label}
                size="small"
                color={tag.color}
                variant="filled"
                sx={{
                  borderRadius: 2,
                  fontWeight: 600,
                  fontSize: '0.75rem',
                  height: 28,
                  background: alpha(theme.palette[tag.color].main, 0.9),
                  backdropFilter: 'blur(10px)',
                  border: `1px solid ${alpha(theme.palette[tag.color].main, 0.3)}`,
                  boxShadow: `0 2px 8px ${alpha(theme.palette[tag.color].main, 0.3)}`,
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    transform: 'scale(1.05)',
                    boxShadow: `0 4px 12px ${alpha(theme.palette[tag.color].main, 0.4)}`,
                  },
                  '& .MuiChip-icon': {
                    fontSize: '0.875rem',
                  },
                }}
              />
            </Tooltip>
          </motion.div>
        ))}
      </Stack>
    </Box>
  );
}
