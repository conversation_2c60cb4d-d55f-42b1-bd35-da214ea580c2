"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Delete.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Warning.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ModelSelector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/ModelSelector */ \"(app-pages-browser)/./app/components/ModelSelector.tsx\");\n/* harmony import */ var _components_MCPServerManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/MCPServerManager */ \"(app-pages-browser)/./app/components/MCPServerManager.tsx\");\n/* harmony import */ var _components_ChatWindow_ChatWindow__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/ChatWindow/ChatWindow */ \"(app-pages-browser)/./app/components/ChatWindow/ChatWindow.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    var _getActiveConversation, _getActiveConversation1;\n    _s();\n    const theme = (0,_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(theme.breakpoints.down('md'));\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!isMobile);\n    const [mcpConfig, setMcpConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mcpServers: {}\n    });\n    // Save MCP config when it changes\n    const updateMcpConfig = async (newConfig)=>{\n        setMcpConfig(newConfig);\n        try {\n            const response = await fetch('/api/mcp', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'update_config',\n                    config: newConfig\n                })\n            });\n            if (!response.ok) {\n                console.error('Failed to save MCP config');\n            }\n        } catch (error) {\n            console.error('Error saving MCP config:', error);\n        }\n    };\n    const [availableTools, setAvailableTools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeConversationId, setActiveConversationId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modelCapabilities, setModelCapabilities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            loadMCPConfig();\n            loadConversations();\n        }\n    }[\"HomePage.useEffect\"], []);\n    // Reload MCP config when servers change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            const interval = setInterval({\n                \"HomePage.useEffect.interval\": ()=>{\n                    loadMCPConfig();\n                }\n            }[\"HomePage.useEffect.interval\"], 5000); // Reload every 5 seconds\n            return ({\n                \"HomePage.useEffect\": ()=>clearInterval(interval)\n            })[\"HomePage.useEffect\"];\n        }\n    }[\"HomePage.useEffect\"], []);\n    // Auto-create a new chat if none exists\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            if (conversations.length === 0 && !activeConversationId) {\n                createNewConversation();\n            }\n        }\n    }[\"HomePage.useEffect\"], [\n        conversations.length,\n        activeConversationId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            setSidebarOpen(!isMobile);\n        }\n    }[\"HomePage.useEffect\"], [\n        isMobile\n    ]);\n    const loadMCPConfig = async ()=>{\n        try {\n            console.log('🔍 Loading MCP config from /api/mcp...');\n            const response = await fetch('/api/mcp');\n            if (response.ok) {\n                const data = await response.json();\n                console.log('✅ Loaded MCP data:', data);\n                console.log('✅ Available tools from API:', data.tools);\n                setMcpConfig({\n                    mcpServers: data.mcpServers || {}\n                });\n                setAvailableTools(data.tools || []);\n                console.log('✅ Set available tools state:', data.tools || []);\n            } else {\n                console.error('❌ Failed to load MCP config:', response.status);\n            }\n        } catch (error) {\n            console.error('❌ Error loading MCP config:', error);\n        }\n    };\n    const loadConversations = ()=>{\n        // Load conversations from localStorage\n        const saved = localStorage.getItem('ollama-chat-conversations');\n        if (saved) {\n            try {\n                const parsed = JSON.parse(saved);\n                setConversations(parsed.map((conv)=>({\n                        ...conv,\n                        timestamp: new Date(conv.timestamp),\n                        messages: conv.messages.map((msg)=>({\n                                ...msg,\n                                timestamp: new Date(msg.timestamp)\n                            }))\n                    })));\n            } catch (error) {\n                console.error('Error loading conversations:', error);\n            }\n        }\n    };\n    const saveConversations = (convs)=>{\n        localStorage.setItem('ollama-chat-conversations', JSON.stringify(convs));\n    };\n    const createNewConversation = ()=>{\n        const newConversation = {\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n            title: 'New Conversation',\n            lastMessage: '',\n            timestamp: new Date(),\n            messageCount: 0,\n            messages: []\n        };\n        const updatedConversations = [\n            newConversation,\n            ...conversations\n        ];\n        setConversations(updatedConversations);\n        setActiveConversationId(newConversation.id);\n        saveConversations(updatedConversations);\n        if (isMobile) {\n            setSidebarOpen(false);\n        }\n    };\n    const selectConversation = (id)=>{\n        setActiveConversationId(id);\n        if (isMobile) {\n            setSidebarOpen(false);\n        }\n    };\n    const deleteConversation = (id)=>{\n        const updatedConversations = conversations.filter((conv)=>conv.id !== id);\n        setConversations(updatedConversations);\n        saveConversations(updatedConversations);\n        if (activeConversationId === id) {\n            setActiveConversationId(updatedConversations.length > 0 ? updatedConversations[0].id : null);\n        }\n    };\n    const handleDeleteCurrentChat = ()=>{\n        if (activeConversationId) {\n            deleteConversation(activeConversationId);\n            setDeleteDialogOpen(false);\n        }\n    };\n    const updateConversationWithMessage = (message)=>{\n        const activeConv = conversations.find((conv)=>conv.id === activeConversationId);\n        if (!activeConv) return;\n        const updatedConversation = {\n            ...activeConv,\n            messages: [\n                ...activeConv.messages,\n                message\n            ],\n            lastMessage: message.content,\n            timestamp: new Date(),\n            messageCount: activeConv.messageCount + 1,\n            title: activeConv.title === 'New Conversation' && message.role === 'user' ? message.content.substring(0, 50) + (message.content.length > 50 ? '...' : '') : activeConv.title\n        };\n        const updatedConversations = conversations.map((conv)=>conv.id === activeConversationId ? updatedConversation : conv);\n        setConversations(updatedConversations);\n        saveConversations(updatedConversations);\n    };\n    const getActiveConversation = ()=>{\n        return conversations.find((conv)=>conv.id === activeConversationId) || null;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n        initial: {\n            opacity: 0\n        },\n        animate: {\n            opacity: 1\n        },\n        transition: {\n            duration: 0.5\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            sx: {\n                height: '100vh',\n                display: 'flex',\n                overflow: 'hidden'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    sx: {\n                        width: sidebarOpen ? 280 : 0,\n                        transition: 'width 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                        overflow: 'hidden',\n                        borderRight: sidebarOpen ? '1px solid rgba(255, 255, 255, 0.08)' : 'none'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        sx: {\n                            width: 280,\n                            height: '100%',\n                            display: 'flex',\n                            flexDirection: 'column',\n                            backdropFilter: 'blur(20px)',\n                            background: 'rgba(255, 255, 255, 0.02)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                sx: {\n                                    p: 2,\n                                    borderBottom: '1px solid rgba(255, 255, 255, 0.05)'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        sx: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                sx: {\n                                                    flex: 1\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModelSelector__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    selectedModel: selectedModel,\n                                                    onModelSelect: setSelectedModel,\n                                                    onModelCapabilitiesChange: setModelCapabilities\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MCPServerManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                config: mcpConfig,\n                                                onConfigUpdate: updateMcpConfig,\n                                                availableTools: availableTools\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        sx: {\n                                            mt: 1.5,\n                                            display: 'flex',\n                                            gap: 1,\n                                            alignItems: 'center'\n                                        },\n                                        children: [\n                                            availableTools.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                sx: {\n                                                    px: 1.5,\n                                                    py: 0.5,\n                                                    borderRadius: 1,\n                                                    background: 'rgba(76, 175, 80, 0.1)',\n                                                    fontSize: '0.7rem',\n                                                    color: 'rgba(255, 255, 255, 0.8)',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: 0.5\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        sx: {\n                                                            width: 4,\n                                                            height: 4,\n                                                            borderRadius: '50%',\n                                                            bgcolor: '#4caf50'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    availableTools.length\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this),\n                                            Object.keys(mcpConfig.mcpServers || {}).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                sx: {\n                                                    px: 1.5,\n                                                    py: 0.5,\n                                                    borderRadius: 1,\n                                                    background: 'rgba(33, 150, 243, 0.1)',\n                                                    fontSize: '0.7rem',\n                                                    color: 'rgba(255, 255, 255, 0.8)',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: 0.5\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        sx: {\n                                                            width: 4,\n                                                            height: 4,\n                                                            borderRadius: '50%',\n                                                            bgcolor: '#2196f3'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    Object.keys(mcpConfig.mcpServers).length\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                sx: {\n                                    flex: 1,\n                                    overflow: 'auto',\n                                    p: 1.5\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        sx: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            justifyContent: 'space-between',\n                                            mb: 1.5\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                variant: \"caption\",\n                                                color: \"text.secondary\",\n                                                sx: {\n                                                    fontSize: '0.75rem',\n                                                    fontWeight: 500\n                                                },\n                                                children: \"Conversations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                onClick: createNewConversation,\n                                                sx: {\n                                                    p: 0.5,\n                                                    color: 'rgba(255, 255, 255, 0.6)',\n                                                    '&:hover': {\n                                                        color: 'rgba(255, 255, 255, 0.8)',\n                                                        background: 'rgba(255, 255, 255, 0.05)'\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    sx: {\n                                                        fontSize: '1rem',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: \"+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, this),\n                                    conversations.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        variant: \"body2\",\n                                        color: \"text.secondary\",\n                                        sx: {\n                                            textAlign: 'center',\n                                            py: 3,\n                                            fontSize: '0.8rem'\n                                        },\n                                        children: \"No conversations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 15\n                                    }, this) : conversations.map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            sx: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                p: 1.5,\n                                                mb: 0.5,\n                                                borderRadius: 1.5,\n                                                background: conversation.id === activeConversationId ? 'rgba(255, 255, 255, 0.08)' : 'transparent',\n                                                transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',\n                                                '&:hover': {\n                                                    background: conversation.id === activeConversationId ? 'rgba(255, 255, 255, 0.12)' : 'rgba(255, 255, 255, 0.04)',\n                                                    '& .delete-btn': {\n                                                        opacity: 1\n                                                    }\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    onClick: ()=>selectConversation(conversation.id),\n                                                    sx: {\n                                                        flex: 1,\n                                                        cursor: 'pointer'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            variant: \"body2\",\n                                                            fontWeight: 500,\n                                                            noWrap: true,\n                                                            sx: {\n                                                                fontSize: '0.85rem'\n                                                            },\n                                                            children: conversation.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            variant: \"caption\",\n                                                            color: \"text.secondary\",\n                                                            noWrap: true,\n                                                            sx: {\n                                                                fontSize: '0.7rem'\n                                                            },\n                                                            children: [\n                                                                conversation.messageCount,\n                                                                \" messages\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 19\n                                                }, this),\n                                                conversation.id === activeConversationId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"delete-btn\",\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        setDeleteDialogOpen(true);\n                                                    },\n                                                    sx: {\n                                                        p: 0.5,\n                                                        opacity: 0,\n                                                        transition: 'all 0.2s ease',\n                                                        color: 'rgba(255, 255, 255, 0.4)',\n                                                        '&:hover': {\n                                                            color: '#ff6b6b',\n                                                            background: 'rgba(255, 107, 107, 0.1)'\n                                                        }\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        sx: {\n                                                            fontSize: '0.9rem'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, conversation.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                    lineNumber: 221,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    sx: {\n                        flex: 1,\n                        display: 'flex',\n                        flexDirection: 'column'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            sx: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                px: 2,\n                                py: 1.5,\n                                borderBottom: '1px solid rgba(255, 255, 255, 0.05)',\n                                background: 'rgba(0, 0, 0, 0.1)'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                    sx: {\n                                        mr: 2,\n                                        p: 0.5,\n                                        color: 'rgba(255, 255, 255, 0.7)',\n                                        '&:hover': {\n                                            background: 'rgba(255, 255, 255, 0.05)'\n                                        }\n                                    },\n                                    children: \"☰\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"subtitle1\",\n                                    sx: {\n                                        flex: 1,\n                                        fontSize: '0.9rem',\n                                        fontWeight: 500\n                                    },\n                                    children: ((_getActiveConversation = getActiveConversation()) === null || _getActiveConversation === void 0 ? void 0 : _getActiveConversation.title) || 'Chat'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 11\n                                }, this),\n                                selectedModel && modelCapabilities && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    sx: {\n                                        display: 'flex',\n                                        gap: 0.5,\n                                        alignItems: 'center'\n                                    },\n                                    children: [\n                                        modelCapabilities.supportsTools && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            sx: {\n                                                px: 1,\n                                                py: 0.25,\n                                                borderRadius: 0.75,\n                                                background: 'rgba(76, 175, 80, 0.15)',\n                                                border: '1px solid rgba(76, 175, 80, 0.3)',\n                                                fontSize: '0.7rem',\n                                                color: '#81c784',\n                                                fontWeight: 500\n                                            },\n                                            children: \"Tools\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this),\n                                        modelCapabilities.supportsVision && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            sx: {\n                                                px: 1,\n                                                py: 0.25,\n                                                borderRadius: 0.75,\n                                                background: 'rgba(156, 39, 176, 0.15)',\n                                                border: '1px solid rgba(156, 39, 176, 0.3)',\n                                                fontSize: '0.7rem',\n                                                color: '#ba68c8',\n                                                fontWeight: 500\n                                            },\n                                            children: \"Vision\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 17\n                                        }, this),\n                                        modelCapabilities.thinking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            sx: {\n                                                px: 1,\n                                                py: 0.25,\n                                                borderRadius: 0.75,\n                                                background: 'rgba(255, 152, 0, 0.15)',\n                                                border: '1px solid rgba(255, 152, 0, 0.3)',\n                                                fontSize: '0.7rem',\n                                                color: '#ffb74d',\n                                                fontWeight: 500\n                                            },\n                                            children: \"Thinking\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            sx: {\n                                flex: 1,\n                                overflow: 'hidden'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatWindow_ChatWindow__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                selectedModel: selectedModel,\n                                availableTools: availableTools,\n                                conversation: getActiveConversation(),\n                                onMessageSent: updateConversationWithMessage,\n                                modelCapabilities: modelCapabilities\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                    lineNumber: 387,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    open: deleteDialogOpen,\n                    onClose: ()=>setDeleteDialogOpen(false),\n                    slotProps: {\n                        paper: {\n                            sx: {\n                                backdropFilter: 'blur(20px)',\n                                background: 'rgba(255, 255, 255, 0.1)',\n                                border: '1px solid rgba(255, 255, 255, 0.2)',\n                                color: 'white'\n                            }\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            sx: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                color: 'white'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    sx: {\n                                        mr: 1,\n                                        color: '#ff4444'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 11\n                                }, this),\n                                \"Delete Chat\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                color: \"rgba(255, 255, 255, 0.8)\",\n                                children: [\n                                    'Are you sure you want to delete \"',\n                                    (_getActiveConversation1 = getActiveConversation()) === null || _getActiveConversation1 === void 0 ? void 0 : _getActiveConversation1.title,\n                                    '\"? This action cannot be undone and all messages in this conversation will be permanently lost.'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 498,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 497,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    onClick: ()=>setDeleteDialogOpen(false),\n                                    sx: {\n                                        color: 'rgba(255, 255, 255, 0.7)'\n                                    },\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    onClick: handleDeleteCurrentChat,\n                                    variant: \"contained\",\n                                    sx: {\n                                        bgcolor: '#ff4444',\n                                        '&:hover': {\n                                            bgcolor: '#ff3333'\n                                        }\n                                    },\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 510,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                    lineNumber: 479,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n            lineNumber: 219,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n        lineNumber: 214,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"50Gu0Z6z2VDamiGeKA/2V+EhtzE=\", false, function() {\n    return [\n        _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});