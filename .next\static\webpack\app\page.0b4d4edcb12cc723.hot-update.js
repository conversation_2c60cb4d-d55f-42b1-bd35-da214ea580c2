"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/useMediaQuery/index.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Dialog,DialogActions,DialogContent,DialogTitle,IconButton,Typography,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Delete.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Warning!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Warning.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ModelSelector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/ModelSelector */ \"(app-pages-browser)/./app/components/ModelSelector.tsx\");\n/* harmony import */ var _components_MCPServerManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/MCPServerManager */ \"(app-pages-browser)/./app/components/MCPServerManager.tsx\");\n/* harmony import */ var _components_ChatWindow_ChatWindow__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/ChatWindow/ChatWindow */ \"(app-pages-browser)/./app/components/ChatWindow/ChatWindow.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    var _getActiveConversation, _getActiveConversation1;\n    _s();\n    const theme = (0,_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(theme.breakpoints.down('md'));\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!isMobile);\n    const [mcpConfig, setMcpConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        mcpServers: {}\n    });\n    // Save MCP config when it changes\n    const updateMcpConfig = async (newConfig)=>{\n        setMcpConfig(newConfig);\n        try {\n            const response = await fetch('/api/mcp', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'update_config',\n                    config: newConfig\n                })\n            });\n            if (!response.ok) {\n                console.error('Failed to save MCP config');\n            }\n        } catch (error) {\n            console.error('Error saving MCP config:', error);\n        }\n    };\n    const [availableTools, setAvailableTools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeConversationId, setActiveConversationId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modelCapabilities, setModelCapabilities] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deleteDialogOpen, setDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            loadMCPConfig();\n            loadConversations();\n        }\n    }[\"HomePage.useEffect\"], []);\n    // Auto-create a new chat if none exists\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            if (conversations.length === 0 && !activeConversationId) {\n                createNewConversation();\n            }\n        }\n    }[\"HomePage.useEffect\"], [\n        conversations.length,\n        activeConversationId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HomePage.useEffect\": ()=>{\n            setSidebarOpen(!isMobile);\n        }\n    }[\"HomePage.useEffect\"], [\n        isMobile\n    ]);\n    const loadMCPConfig = async ()=>{\n        try {\n            const response = await fetch('/api/mcp');\n            if (response.ok) {\n                const data = await response.json();\n                console.log('Loaded MCP data:', data);\n                console.log('Available tools:', data.tools);\n                setMcpConfig({\n                    mcpServers: data.mcpServers || {}\n                });\n                setAvailableTools(data.tools || []);\n                console.log('Set available tools state:', data.tools || []);\n            }\n        } catch (error) {\n            console.error('Error loading MCP config:', error);\n        }\n    };\n    const loadConversations = ()=>{\n        // Load conversations from localStorage\n        const saved = localStorage.getItem('ollama-chat-conversations');\n        if (saved) {\n            try {\n                const parsed = JSON.parse(saved);\n                setConversations(parsed.map((conv)=>({\n                        ...conv,\n                        timestamp: new Date(conv.timestamp),\n                        messages: conv.messages.map((msg)=>({\n                                ...msg,\n                                timestamp: new Date(msg.timestamp)\n                            }))\n                    })));\n            } catch (error) {\n                console.error('Error loading conversations:', error);\n            }\n        }\n    };\n    const saveConversations = (convs)=>{\n        localStorage.setItem('ollama-chat-conversations', JSON.stringify(convs));\n    };\n    const createNewConversation = ()=>{\n        const newConversation = {\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n            title: 'New Conversation',\n            lastMessage: '',\n            timestamp: new Date(),\n            messageCount: 0,\n            messages: []\n        };\n        const updatedConversations = [\n            newConversation,\n            ...conversations\n        ];\n        setConversations(updatedConversations);\n        setActiveConversationId(newConversation.id);\n        saveConversations(updatedConversations);\n        if (isMobile) {\n            setSidebarOpen(false);\n        }\n    };\n    const selectConversation = (id)=>{\n        setActiveConversationId(id);\n        if (isMobile) {\n            setSidebarOpen(false);\n        }\n    };\n    const deleteConversation = (id)=>{\n        const updatedConversations = conversations.filter((conv)=>conv.id !== id);\n        setConversations(updatedConversations);\n        saveConversations(updatedConversations);\n        if (activeConversationId === id) {\n            setActiveConversationId(updatedConversations.length > 0 ? updatedConversations[0].id : null);\n        }\n    };\n    const handleDeleteCurrentChat = ()=>{\n        if (activeConversationId) {\n            deleteConversation(activeConversationId);\n            setDeleteDialogOpen(false);\n        }\n    };\n    const updateConversationWithMessage = (message)=>{\n        const activeConv = conversations.find((conv)=>conv.id === activeConversationId);\n        if (!activeConv) return;\n        const updatedConversation = {\n            ...activeConv,\n            messages: [\n                ...activeConv.messages,\n                message\n            ],\n            lastMessage: message.content,\n            timestamp: new Date(),\n            messageCount: activeConv.messageCount + 1,\n            title: activeConv.title === 'New Conversation' && message.role === 'user' ? message.content.substring(0, 50) + (message.content.length > 50 ? '...' : '') : activeConv.title\n        };\n        const updatedConversations = conversations.map((conv)=>conv.id === activeConversationId ? updatedConversation : conv);\n        setConversations(updatedConversations);\n        saveConversations(updatedConversations);\n    };\n    const getActiveConversation = ()=>{\n        return conversations.find((conv)=>conv.id === activeConversationId) || null;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n        initial: {\n            opacity: 0\n        },\n        animate: {\n            opacity: 1\n        },\n        transition: {\n            duration: 0.5\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n            sx: {\n                height: '100vh',\n                display: 'flex',\n                overflow: 'hidden'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    sx: {\n                        width: sidebarOpen ? 280 : 0,\n                        transition: 'width 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n                        overflow: 'hidden',\n                        borderRight: sidebarOpen ? '1px solid rgba(255, 255, 255, 0.08)' : 'none'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        sx: {\n                            width: 280,\n                            height: '100%',\n                            display: 'flex',\n                            flexDirection: 'column',\n                            backdropFilter: 'blur(20px)',\n                            background: 'rgba(255, 255, 255, 0.02)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                sx: {\n                                    p: 2,\n                                    borderBottom: '1px solid rgba(255, 255, 255, 0.05)'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        sx: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                sx: {\n                                                    flex: 1\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModelSelector__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    selectedModel: selectedModel,\n                                                    onModelSelect: setSelectedModel,\n                                                    onModelCapabilitiesChange: setModelCapabilities\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MCPServerManager__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                config: mcpConfig,\n                                                onConfigUpdate: setMcpConfig,\n                                                availableTools: availableTools\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        sx: {\n                                            mt: 1.5,\n                                            display: 'flex',\n                                            gap: 1,\n                                            alignItems: 'center'\n                                        },\n                                        children: [\n                                            availableTools.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                sx: {\n                                                    px: 1.5,\n                                                    py: 0.5,\n                                                    borderRadius: 1,\n                                                    background: 'rgba(76, 175, 80, 0.1)',\n                                                    fontSize: '0.7rem',\n                                                    color: 'rgba(255, 255, 255, 0.8)',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: 0.5\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        sx: {\n                                                            width: 4,\n                                                            height: 4,\n                                                            borderRadius: '50%',\n                                                            bgcolor: '#4caf50'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    availableTools.length\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, this),\n                                            Object.keys(mcpConfig.mcpServers || {}).length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                sx: {\n                                                    px: 1.5,\n                                                    py: 0.5,\n                                                    borderRadius: 1,\n                                                    background: 'rgba(33, 150, 243, 0.1)',\n                                                    fontSize: '0.7rem',\n                                                    color: 'rgba(255, 255, 255, 0.8)',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: 0.5\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        sx: {\n                                                            width: 4,\n                                                            height: 4,\n                                                            borderRadius: '50%',\n                                                            bgcolor: '#2196f3'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    Object.keys(mcpConfig.mcpServers).length\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                sx: {\n                                    flex: 1,\n                                    overflow: 'auto',\n                                    p: 1.5\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        sx: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            justifyContent: 'space-between',\n                                            mb: 1.5\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                variant: \"caption\",\n                                                color: \"text.secondary\",\n                                                sx: {\n                                                    fontSize: '0.75rem',\n                                                    fontWeight: 500\n                                                },\n                                                children: \"Conversations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                onClick: createNewConversation,\n                                                sx: {\n                                                    p: 0.5,\n                                                    color: 'rgba(255, 255, 255, 0.6)',\n                                                    '&:hover': {\n                                                        color: 'rgba(255, 255, 255, 0.8)',\n                                                        background: 'rgba(255, 255, 255, 0.05)'\n                                                    }\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    sx: {\n                                                        fontSize: '1rem',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: \"+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 13\n                                    }, this),\n                                    conversations.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        variant: \"body2\",\n                                        color: \"text.secondary\",\n                                        sx: {\n                                            textAlign: 'center',\n                                            py: 3,\n                                            fontSize: '0.8rem'\n                                        },\n                                        children: \"No conversations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this) : conversations.map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            sx: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                p: 1.5,\n                                                mb: 0.5,\n                                                borderRadius: 1.5,\n                                                background: conversation.id === activeConversationId ? 'rgba(255, 255, 255, 0.08)' : 'transparent',\n                                                transition: 'all 0.2s cubic-bezier(0.4, 0, 0.2, 1)',\n                                                '&:hover': {\n                                                    background: conversation.id === activeConversationId ? 'rgba(255, 255, 255, 0.12)' : 'rgba(255, 255, 255, 0.04)',\n                                                    '& .delete-btn': {\n                                                        opacity: 1\n                                                    }\n                                                }\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    onClick: ()=>selectConversation(conversation.id),\n                                                    sx: {\n                                                        flex: 1,\n                                                        cursor: 'pointer'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            variant: \"body2\",\n                                                            fontWeight: 500,\n                                                            noWrap: true,\n                                                            sx: {\n                                                                fontSize: '0.85rem'\n                                                            },\n                                                            children: conversation.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            variant: \"caption\",\n                                                            color: \"text.secondary\",\n                                                            noWrap: true,\n                                                            sx: {\n                                                                fontSize: '0.7rem'\n                                                            },\n                                                            children: [\n                                                                conversation.messageCount,\n                                                                \" messages\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 19\n                                                }, this),\n                                                conversation.id === activeConversationId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"delete-btn\",\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        setDeleteDialogOpen(true);\n                                                    },\n                                                    sx: {\n                                                        p: 0.5,\n                                                        opacity: 0,\n                                                        transition: 'all 0.2s ease',\n                                                        color: 'rgba(255, 255, 255, 0.4)',\n                                                        '&:hover': {\n                                                            color: '#ff6b6b',\n                                                            background: 'rgba(255, 107, 107, 0.1)'\n                                                        }\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        sx: {\n                                                            fontSize: '0.9rem'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, conversation.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    sx: {\n                        flex: 1,\n                        display: 'flex',\n                        flexDirection: 'column'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            sx: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                px: 2,\n                                py: 1.5,\n                                borderBottom: '1px solid rgba(255, 255, 255, 0.05)',\n                                background: 'rgba(0, 0, 0, 0.1)'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                    sx: {\n                                        mr: 2,\n                                        p: 0.5,\n                                        color: 'rgba(255, 255, 255, 0.7)',\n                                        '&:hover': {\n                                            background: 'rgba(255, 255, 255, 0.05)'\n                                        }\n                                    },\n                                    children: \"☰\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"subtitle1\",\n                                    sx: {\n                                        flex: 1,\n                                        fontSize: '0.9rem',\n                                        fontWeight: 500\n                                    },\n                                    children: ((_getActiveConversation = getActiveConversation()) === null || _getActiveConversation === void 0 ? void 0 : _getActiveConversation.title) || 'Chat'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 11\n                                }, this),\n                                selectedModel && modelCapabilities && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    sx: {\n                                        display: 'flex',\n                                        gap: 0.5,\n                                        alignItems: 'center'\n                                    },\n                                    children: [\n                                        modelCapabilities.supportsTools && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            sx: {\n                                                px: 1,\n                                                py: 0.25,\n                                                borderRadius: 0.75,\n                                                background: 'rgba(76, 175, 80, 0.15)',\n                                                border: '1px solid rgba(76, 175, 80, 0.3)',\n                                                fontSize: '0.7rem',\n                                                color: '#81c784',\n                                                fontWeight: 500\n                                            },\n                                            children: \"Tools\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 17\n                                        }, this),\n                                        modelCapabilities.supportsVision && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            sx: {\n                                                px: 1,\n                                                py: 0.25,\n                                                borderRadius: 0.75,\n                                                background: 'rgba(156, 39, 176, 0.15)',\n                                                border: '1px solid rgba(156, 39, 176, 0.3)',\n                                                fontSize: '0.7rem',\n                                                color: '#ba68c8',\n                                                fontWeight: 500\n                                            },\n                                            children: \"Vision\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, this),\n                                        modelCapabilities.thinking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            sx: {\n                                                px: 1,\n                                                py: 0.25,\n                                                borderRadius: 0.75,\n                                                background: 'rgba(255, 152, 0, 0.15)',\n                                                border: '1px solid rgba(255, 152, 0, 0.3)',\n                                                fontSize: '0.7rem',\n                                                color: '#ffb74d',\n                                                fontWeight: 500\n                                            },\n                                            children: \"Thinking\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            sx: {\n                                flex: 1,\n                                overflow: 'hidden'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatWindow_ChatWindow__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                selectedModel: selectedModel,\n                                availableTools: availableTools,\n                                conversation: getActiveConversation(),\n                                onMessageSent: updateConversationWithMessage,\n                                modelCapabilities: modelCapabilities\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 455,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                    lineNumber: 375,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    open: deleteDialogOpen,\n                    onClose: ()=>setDeleteDialogOpen(false),\n                    slotProps: {\n                        paper: {\n                            sx: {\n                                backdropFilter: 'blur(20px)',\n                                background: 'rgba(255, 255, 255, 0.1)',\n                                border: '1px solid rgba(255, 255, 255, 0.2)',\n                                color: 'white'\n                            }\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            sx: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                color: 'white'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    sx: {\n                                        mr: 1,\n                                        color: '#ff4444'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 11\n                                }, this),\n                                \"Delete Chat\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                color: \"rgba(255, 255, 255, 0.8)\",\n                                children: [\n                                    'Are you sure you want to delete \"',\n                                    (_getActiveConversation1 = getActiveConversation()) === null || _getActiveConversation1 === void 0 ? void 0 : _getActiveConversation1.title,\n                                    '\"? This action cannot be undone and all messages in this conversation will be permanently lost.'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 485,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    onClick: ()=>setDeleteDialogOpen(false),\n                                    sx: {\n                                        color: 'rgba(255, 255, 255, 0.7)'\n                                    },\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    onClick: handleDeleteCurrentChat,\n                                    variant: \"contained\",\n                                    sx: {\n                                        bgcolor: '#ff4444',\n                                        '&:hover': {\n                                            bgcolor: '#ff3333'\n                                        }\n                                    },\n                                    children: \"Delete\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                            lineNumber: 491,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n                    lineNumber: 467,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n            lineNumber: 207,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\page.tsx\",\n        lineNumber: 202,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"i8BljwjraogOEx2cyOlNio3iKnA=\", false, function() {\n    return [\n        _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        _barrel_optimize_names_Box_Button_Dialog_DialogActions_DialogContent_DialogTitle_IconButton_Typography_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});