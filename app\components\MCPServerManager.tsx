'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>pography,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress,
  useTheme,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Settings as SettingsIcon,
  Edit as EditIcon,
} from '@mui/icons-material';

import { MCPConfig, MCPServerConfig } from '@/types';

interface MCPServerManagerProps {
  config: MCPConfig;
  onConfigUpdate: (config: MCPConfig) => void;
  availableTools: any[];
}

interface ServerStatus {
  [serverName: string]: {
    running: boolean;
    tools: number;
    error?: string;
  };
}

export default function MCPServerManager({
  config,
  onConfigUpdate,
  availableTools,
}: MCPServerManagerProps) {
  const [open, setOpen] = useState(false);
  const [serverStatus, setServerStatus] = useState<ServerStatus>({});
  const [loading, setLoading] = useState<string | null>(null);
  const [editDialog, setEditDialog] = useState(false);
  const [editingServer, setEditingServer] = useState<string | null>(null);
  const [serverJson, setServerJson] = useState('');
  const [jsonError, setJsonError] = useState('');
  const theme = useTheme();

  useEffect(() => {
    // Initialize server status
    const status: ServerStatus = {};
    Object.keys(config.mcpServers || {}).forEach(serverName => {
      const serverTools = availableTools.filter(tool => tool.serverName === serverName);
      status[serverName] = {
        running: serverTools.length > 0,
        tools: serverTools.length,
      };
    });
    setServerStatus(status);
  }, [config, availableTools]);

  const handleStartServer = async (serverName: string) => {
    setLoading(serverName);
    try {
      const response = await fetch('/api/mcp', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'start_server',
          serverName,
        }),
      });

      if (response.ok) {
        setServerStatus(prev => ({
          ...prev,
          [serverName]: { ...prev[serverName], running: true, error: undefined },
        }));
      } else {
        const error = await response.json();
        setServerStatus(prev => ({
          ...prev,
          [serverName]: { ...prev[serverName], running: false, error: error.error },
        }));
      }
    } catch (error) {
      setServerStatus(prev => ({
        ...prev,
        [serverName]: { ...prev[serverName], running: false, error: 'Failed to start server' },
      }));
    } finally {
      setLoading(null);
    }
  };

  const handleStopServer = async (serverName: string) => {
    setLoading(serverName);
    try {
      const response = await fetch('/api/mcp', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'stop_server',
          serverName,
        }),
      });

      if (response.ok) {
        setServerStatus(prev => ({
          ...prev,
          [serverName]: { ...prev[serverName], running: false, error: undefined },
        }));
      }
    } catch (error) {
      console.error('Failed to stop server:', error);
    } finally {
      setLoading(null);
    }
  };

  const handleEditServer = (serverName: string) => {
    const serverConfig = config.mcpServers[serverName];
    if (serverConfig) {
      setEditingServer(serverName);
      // Show the full structure for easier editing
      setServerJson(JSON.stringify({
        mcpServers: {
          [serverName]: serverConfig
        }
      }, null, 2));
      setJsonError('');
      setEditDialog(true);
    }
  };

  const handleAddNewServer = () => {
    setEditingServer(null);
    setServerJson(JSON.stringify({
      "mcpServers": {
        "duckduckgo-mcp-server": {
          "command": "npx",
          "args": [
            "-y",
            "@smithery/cli@latest",
            "run",
            "@nickclyde/duckduckgo-mcp-server",
            "--key",
            "your-api-key-here",
            "--profile",
            "your-profile-here"
          ],
          "description": "DuckDuckGo search MCP server via Smithery"
        }
      }
    }, null, 2));
    setJsonError('');
    setEditDialog(true);
  };

  const handleSaveServer = () => {
    try {
      const parsedConfig = JSON.parse(serverJson);

      let newConfig;

      // Check if the JSON contains the full mcpServers structure
      if (parsedConfig.mcpServers && typeof parsedConfig.mcpServers === 'object') {
        // Full structure provided - merge with existing config
        newConfig = {
          ...config,
          mcpServers: {
            ...config.mcpServers,
            ...parsedConfig.mcpServers,
          },
        };
      } else {
        // Single server config provided
        // Validate required fields
        if (!parsedConfig.command || !Array.isArray(parsedConfig.args)) {
          throw new Error('Server config must have "command" (string) and "args" (array), or provide full mcpServers structure');
        }

        let serverName = editingServer;
        if (!serverName) {
          // For new servers, generate a name or ask for one
          serverName = `server_${Date.now()}`;
        }

        newConfig = {
          ...config,
          mcpServers: {
            ...config.mcpServers,
            [serverName]: parsedConfig,
          },
        };
      }

      onConfigUpdate(newConfig);
      setEditDialog(false);
      setJsonError('');
    } catch (error) {
      setJsonError(error instanceof Error ? error.message : 'Invalid JSON');
    }
  };

  const handleRemoveServer = (serverName: string) => {
    const newConfig = { ...config };
    delete newConfig.mcpServers[serverName];
    onConfigUpdate(newConfig);
  };

  return (
    <>
      <IconButton
        onClick={() => setOpen(true)}
        sx={{
          p: 0.5,
          color: 'rgba(255, 255, 255, 0.6)',
          '&:hover': {
            color: 'rgba(255, 255, 255, 0.8)',
            background: 'rgba(255, 255, 255, 0.05)',
          },
        }}
      >
        <SettingsIcon fontSize="small" />
      </IconButton>

      <Dialog
        open={open}
        onClose={() => setOpen(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            background: 'rgba(0, 0, 0, 0.9)',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
          },
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h6" sx={{ fontSize: '1.1rem', fontWeight: 500 }}>
              Servers
            </Typography>
            <IconButton
              onClick={handleAddNewServer}
              sx={{
                color: 'rgba(255, 255, 255, 0.8)',
                '&:hover': {
                  background: 'rgba(255, 255, 255, 0.1)',
                },
              }}
            >
              <AddIcon fontSize="small" />
            </IconButton>
          </Box>
        </DialogTitle>

        <DialogContent sx={{ py: 1 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            {Object.entries(config.mcpServers || {}).map(([serverName, serverConfig]) => {
              const status = serverStatus[serverName] || { running: false, tools: 0 };
              const isLoading = loading === serverName;

              return (
                <Box
                  key={serverName}
                  sx={{
                    p: 2,
                    borderRadius: 1.5,
                    background: 'rgba(255, 255, 255, 0.03)',
                    border: '1px solid rgba(255, 255, 255, 0.08)',
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      background: 'rgba(255, 255, 255, 0.06)',
                    },
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                      <Box
                        sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          bgcolor: status.running ? '#4caf50' : '#f44336',
                        }}
                      />
                      <Typography variant="body2" fontWeight={500} sx={{ fontSize: '0.9rem' }}>
                        {serverName}
                      </Typography>
                      {status.tools > 0 && (
                        <Box sx={{
                          px: 1,
                          py: 0.25,
                          borderRadius: 0.75,
                          background: 'rgba(33, 150, 243, 0.2)',
                          fontSize: '0.7rem',
                          color: '#64b5f6',
                        }}>
                          {status.tools}
                        </Box>
                      )}
                    </Box>

                    <Box sx={{ display: 'flex', gap: 0.5 }}>
                      <IconButton
                        onClick={() => status.running ? handleStopServer(serverName) : handleStartServer(serverName)}
                        disabled={isLoading}
                        sx={{
                          p: 0.5,
                          color: status.running ? '#f44336' : '#4caf50',
                          '&:hover': {
                            background: status.running ? 'rgba(244, 67, 54, 0.1)' : 'rgba(76, 175, 80, 0.1)',
                          },
                        }}
                      >
                        {isLoading ? (
                          <CircularProgress size={16} />
                        ) : status.running ? (
                          <StopIcon fontSize="small" />
                        ) : (
                          <PlayIcon fontSize="small" />
                        )}
                      </IconButton>

                      <IconButton
                        onClick={() => handleEditServer(serverName)}
                        sx={{
                          p: 0.5,
                          color: 'rgba(255, 255, 255, 0.6)',
                          '&:hover': {
                            background: 'rgba(255, 255, 255, 0.1)',
                            color: 'rgba(255, 255, 255, 0.8)',
                          },
                        }}
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>

                      <IconButton
                        onClick={() => handleRemoveServer(serverName)}
                        sx={{
                          p: 0.5,
                          color: 'rgba(255, 255, 255, 0.4)',
                          '&:hover': {
                            background: 'rgba(244, 67, 54, 0.1)',
                            color: '#f44336',
                          },
                        }}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  </Box>

                  {serverConfig.description && (
                    <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                      {serverConfig.description}
                    </Typography>
                  )}

                  {status.error && (
                    <Typography variant="caption" color="error" sx={{ fontSize: '0.75rem', mt: 0.5, display: 'block' }}>
                      {status.error}
                    </Typography>
                  )}
                </Box>
              );
            })}
          </Box>
        </DialogContent>

        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button
            onClick={() => setOpen(false)}
            sx={{
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: '0.85rem',
              '&:hover': {
                background: 'rgba(255, 255, 255, 0.05)',
              },
            }}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* JSON Editor Dialog */}
      <Dialog
        open={editDialog}
        onClose={() => setEditDialog(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            background: 'rgba(0, 0, 0, 0.9)',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
          },
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          <Typography variant="h6" sx={{ fontSize: '1.1rem', fontWeight: 500 }}>
            {editingServer ? `Edit ${editingServer}` : 'Add Server'}
          </Typography>
        </DialogTitle>
        <DialogContent sx={{ py: 1 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              placeholder='Paste your MCP server JSON here...'
              value={serverJson}
              onChange={(e) => {
                setServerJson(e.target.value);
                setJsonError('');
              }}
              multiline
              rows={10}
              fullWidth
              variant="outlined"
              error={!!jsonError}
              helperText={jsonError || 'Supports both single server config or full mcpServers structure'}
              sx={{
                '& .MuiOutlinedInput-root': {
                  background: 'rgba(255, 255, 255, 0.03)',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                  borderRadius: 1.5,
                  fontFamily: 'Monaco, Consolas, "Courier New", monospace',
                  fontSize: '0.8rem',
                  '&:hover': {
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                  },
                  '&.Mui-focused': {
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                  },
                },
                '& .MuiInputBase-input': {
                  color: 'rgba(255, 255, 255, 0.9)',
                },
                '& .MuiFormHelperText-root': {
                  fontSize: '0.75rem',
                },
              }}
            />
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button
            onClick={() => setEditDialog(false)}
            sx={{
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: '0.85rem',
              '&:hover': {
                background: 'rgba(255, 255, 255, 0.05)',
              },
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSaveServer}
            variant="contained"
            disabled={!serverJson.trim()}
            sx={{
              background: 'rgba(255, 255, 255, 0.9)',
              color: '#000',
              fontSize: '0.85rem',
              '&:hover': {
                background: 'rgba(255, 255, 255, 1)',
              },
              '&:disabled': {
                background: 'rgba(255, 255, 255, 0.2)',
                color: 'rgba(255, 255, 255, 0.3)',
              },
            }}
          >
            {editingServer ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
