'use client';

import React, { useRef, useState } from 'react';
import {
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Box,
  Typography,
  useTheme,
} from '@mui/material';
import {
  AttachFile as AttachFileIcon,
  Image as ImageIcon,
  Description as DocumentIcon,
  Close as CloseIcon,
} from '@mui/icons-material';

interface FileAttachmentProps {
  onFilesSelected: (files: File[]) => void;
  attachedFiles: File[];
  onRemoveFile: (index: number) => void;
  disabled?: boolean;
}

const ACCEPTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/gif',
  'image/webp',
  'image/svg+xml'
];

const ACCEPTED_DOCUMENT_TYPES = [
  'application/pdf',
  'text/plain',
  'text/markdown',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation'
];

export default function FileAttachment({
  onFilesSelected,
  attachedFiles,
  onRemoveFile,
  disabled = false,
}: FileAttachmentProps) {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);
  const documentInputRef = useRef<HTMLInputElement>(null);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleImageSelect = () => {
    imageInputRef.current?.click();
    handleClose();
  };

  const handleDocumentSelect = () => {
    documentInputRef.current?.click();
    handleClose();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length > 0) {
      onFilesSelected(files);
    }
    // Reset input value to allow selecting the same file again
    event.target.value = '';
  };

  const getFileIcon = (file: File) => {
    if (ACCEPTED_IMAGE_TYPES.includes(file.type)) {
      return <ImageIcon fontSize="small" />;
    }
    return <DocumentIcon fontSize="small" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Box>
      {/* Simple Attachment Button */}
      <IconButton
        onClick={handleClick}
        disabled={disabled}
        sx={{
          p: 0.5,
          color: 'rgba(255, 255, 255, 0.6)',
          '&:hover': {
            color: 'rgba(255, 255, 255, 0.8)',
            background: 'rgba(255, 255, 255, 0.05)',
          },
          '&:disabled': {
            opacity: 0.3,
          },
        }}
      >
        <AttachFileIcon fontSize="small" />
      </IconButton>

      {/* Minimal File Type Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
        PaperProps={{
          sx: {
            borderRadius: 1.5,
            mt: 0.5,
            background: 'rgba(0, 0, 0, 0.9)',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
          },
        }}
      >
        <MenuItem onClick={handleImageSelect} sx={{ py: 1, fontSize: '0.85rem' }}>
          <ListItemIcon sx={{ minWidth: 32 }}>
            <ImageIcon fontSize="small" sx={{ color: 'rgba(255, 255, 255, 0.7)' }} />
          </ListItemIcon>
          <ListItemText
            primary="Images"
            primaryTypographyProps={{ fontSize: '0.85rem' }}
            secondary="JPG, PNG, GIF, WebP, SVG"
            secondaryTypographyProps={{ fontSize: '0.7rem' }}
          />
        </MenuItem>
        <MenuItem onClick={handleDocumentSelect} sx={{ py: 1, fontSize: '0.85rem' }}>
          <ListItemIcon sx={{ minWidth: 32 }}>
            <DocumentIcon fontSize="small" sx={{ color: 'rgba(255, 255, 255, 0.7)' }} />
          </ListItemIcon>
          <ListItemText
            primary="Documents"
            primaryTypographyProps={{ fontSize: '0.85rem' }}
            secondary="PDF, TXT, MD, Office files"
            secondaryTypographyProps={{ fontSize: '0.7rem' }}
          />
        </MenuItem>
      </Menu>

      {/* Hidden File Inputs */}
      <input
        ref={imageInputRef}
        type="file"
        accept={ACCEPTED_IMAGE_TYPES.join(',')}
        multiple
        style={{ display: 'none' }}
        onChange={handleFileChange}
      />
      <input
        ref={documentInputRef}
        type="file"
        accept={ACCEPTED_DOCUMENT_TYPES.join(',')}
        multiple
        style={{ display: 'none' }}
        onChange={handleFileChange}
      />

      {/* Minimal Attached Files Display */}
      {attachedFiles.length > 0 && (
        <Box sx={{ mt: 1, display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
          {attachedFiles.map((file, index) => (
            <Box
              key={`${file.name}-${index}`}
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
                px: 1,
                py: 0.5,
                borderRadius: 1,
                background: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                fontSize: '0.75rem',
              }}
            >
              {getFileIcon(file)}
              <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
                {file.name.length > 20 ? `${file.name.substring(0, 20)}...` : file.name}
              </Typography>
              <IconButton
                onClick={() => onRemoveFile(index)}
                sx={{
                  p: 0.25,
                  ml: 0.5,
                  color: 'rgba(255, 255, 255, 0.5)',
                  '&:hover': {
                    color: '#ff6b6b',
                    background: 'rgba(255, 107, 107, 0.1)',
                  },
                }}
              >
                <CloseIcon sx={{ fontSize: '0.8rem' }} />
              </IconButton>
            </Box>
          ))}
        </Box>
      )}
    </Box>
  );
}
