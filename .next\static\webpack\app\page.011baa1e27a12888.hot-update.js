"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/ChatWindow/ChatWindow.tsx":
/*!**************************************************!*\
  !*** ./app/components/ChatWindow/ChatWindow.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatWindow)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _MessageBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MessageBubble */ \"(app-pages-browser)/./app/components/ChatWindow/MessageBubble.tsx\");\n/* harmony import */ var _InputBar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./InputBar */ \"(app-pages-browser)/./app/components/ChatWindow/InputBar.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../TypingIndicator */ \"(app-pages-browser)/./app/components/TypingIndicator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ChatWindow(param) {\n    let { selectedModel, availableTools, conversation, onMessageSent, modelCapabilities } = param;\n    _s();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const messages = (conversation === null || conversation === void 0 ? void 0 : conversation.messages) || [];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatWindow.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatWindow.useEffect\"], [\n        messages\n    ]);\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const handleSendMessage = async (content, attachments)=>{\n        if (!selectedModel) {\n            setError('Please select a model first');\n            return;\n        }\n        // Create user message\n        const userMessage = {\n            id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n            role: 'user',\n            content,\n            timestamp: new Date(),\n            attachments\n        };\n        onMessageSent(userMessage);\n        setLoading(true);\n        setError(null);\n        try {\n            // Check if message has vision content\n            const hasVisionContent = attachments === null || attachments === void 0 ? void 0 : attachments.some((att)=>att.type === 'image');\n            // Validate vision capability\n            if (hasVisionContent && !(modelCapabilities === null || modelCapabilities === void 0 ? void 0 : modelCapabilities.supportsVision)) {\n                setError(\"Model \".concat(selectedModel, \" does not support vision/image processing. Please select a vision-capable model like llava or qwen2-vl.\"));\n                setLoading(false);\n                return;\n            }\n            // Prepare messages for API\n            const conversationMessages = [\n                ...messages,\n                userMessage\n            ];\n            // Only include tools if model supports them\n            const toolsToSend = (modelCapabilities === null || modelCapabilities === void 0 ? void 0 : modelCapabilities.supportsTools) ? availableTools : [];\n            console.log('🔍 ChatWindow - Available tools:', availableTools);\n            console.log('🔍 ChatWindow - Model capabilities:', modelCapabilities);\n            console.log('🔍 ChatWindow - Tools to send:', toolsToSend);\n            console.log('🔍 ChatWindow - availableTools.length:', availableTools.length);\n            // Send to Ollama API\n            const response = await fetch('/api/ollama', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: selectedModel,\n                    messages: conversationMessages.map((msg)=>({\n                            role: msg.role,\n                            content: msg.content,\n                            ...msg.toolCall && {\n                                tool_calls: [\n                                    msg.toolCall\n                                ]\n                            }\n                        })),\n                    tools: toolsToSend,\n                    hasVisionContent\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to get response from Ollama');\n            }\n            const data = await response.json();\n            const assistantMessage = data.message;\n            console.log('Received assistant message:', assistantMessage);\n            console.log('Model capabilities:', modelCapabilities);\n            // Create assistant message\n            const newAssistantMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                role: 'assistant',\n                content: assistantMessage.content,\n                timestamp: new Date()\n            };\n            // Check if there are tool calls (only if model supports tools)\n            if ((modelCapabilities === null || modelCapabilities === void 0 ? void 0 : modelCapabilities.supportsTools) && assistantMessage.tool_calls && assistantMessage.tool_calls.length > 0) {\n                console.log('Tool calls detected:', assistantMessage.tool_calls);\n                const toolCall = assistantMessage.tool_calls[0];\n                // Add an ID to the tool call if it doesn't have one\n                if (!toolCall.id) {\n                    toolCall.id = (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n                }\n                newAssistantMessage.toolCall = toolCall;\n                // Add the assistant message with tool call\n                onMessageSent(newAssistantMessage);\n                // Execute the tool call\n                await executeToolCall(toolCall, [\n                    ...messages,\n                    userMessage,\n                    newAssistantMessage\n                ]);\n            } else {\n                // Add the assistant message\n                onMessageSent(newAssistantMessage);\n            }\n        } catch (err) {\n            console.error('Error sending message:', err);\n            setError('Failed to send message. Please try again.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const executeToolCall = async (toolCall, currentMessages)=>{\n        try {\n            console.log('Executing tool call:', toolCall);\n            // Parse tool arguments\n            let toolArgs;\n            try {\n                toolArgs = typeof toolCall.function.arguments === 'string' ? JSON.parse(toolCall.function.arguments) : toolCall.function.arguments;\n            } catch (parseError) {\n                console.error('Failed to parse tool arguments:', parseError);\n                toolArgs = toolCall.function.arguments;\n            }\n            console.log('Tool arguments:', toolArgs);\n            // Determine server name - use the serverName from the tool if available\n            let serverName = 'duckduckgo-mcp-server'; // default to our Smithery AI server\n            // Check if the tool has a serverName property\n            const toolWithServer = availableTools.find((tool)=>tool.function.name === toolCall.function.name);\n            if (toolWithServer && toolWithServer.serverName) {\n                serverName = toolWithServer.serverName;\n            } else {\n                // Fallback mapping for legacy tools\n                if (toolCall.function.name === 'get_weather') {\n                    serverName = 'weather';\n                } else if (toolCall.function.name === 'search_web') {\n                    serverName = 'duckduckgo-mcp-server';\n                }\n            }\n            console.log(\"\\uD83D\\uDD27 Available tools:\", availableTools);\n            console.log(\"\\uD83D\\uDD27 Tool with server:\", toolWithServer);\n            console.log(\"\\uD83D\\uDD27 Using server: \".concat(serverName, \" for tool: \").concat(toolCall.function.name));\n            // Execute tool via MCP API\n            const response = await fetch('/api/mcp', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'execute_tool',\n                    serverName: serverName,\n                    toolName: toolCall.function.name,\n                    arguments: toolArgs\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to execute tool');\n            }\n            const toolResult = await response.json();\n            // Create tool result message\n            const toolResultMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                role: 'tool',\n                content: JSON.stringify(toolResult.result, null, 2),\n                timestamp: new Date(),\n                toolResult: {\n                    toolCallId: toolCall.id,\n                    result: toolResult.result\n                }\n            };\n            onMessageSent(toolResultMessage);\n            // Send the tool result back to Ollama for final response\n            const finalResponse = await fetch('/api/ollama', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    model: selectedModel,\n                    messages: [\n                        ...currentMessages,\n                        toolResultMessage\n                    ].map((msg)=>({\n                            role: msg.role,\n                            content: msg.content,\n                            ...msg.toolCall && {\n                                tool_calls: [\n                                    msg.toolCall\n                                ]\n                            }\n                        })),\n                    tools: availableTools\n                })\n            });\n            if (finalResponse.ok) {\n                const finalData = await finalResponse.json();\n                const finalMessage = {\n                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                    role: 'assistant',\n                    content: finalData.message.content,\n                    timestamp: new Date()\n                };\n                onMessageSent(finalMessage);\n            }\n        } catch (err) {\n            console.error('Error executing tool call:', err);\n            // Add error message\n            const errorMessage = {\n                id: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                role: 'assistant',\n                content: 'Sorry, I encountered an error while using the tool. Please try again.',\n                timestamp: new Date()\n            };\n            onMessageSent(errorMessage);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        display: \"flex\",\n        flexDirection: \"column\",\n        height: \"100%\",\n        position: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                children: error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -20\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        sx: {\n                            p: 2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            severity: \"error\",\n                            onClose: ()=>setError(null),\n                            sx: {\n                                borderRadius: 2,\n                                backdropFilter: 'blur(20px)',\n                                background: 'rgba(255, 68, 68, 0.1)',\n                                border: '1px solid rgba(255, 68, 68, 0.3)',\n                                color: '#ff4444',\n                                '& .MuiAlert-icon': {\n                                    color: '#ff4444'\n                                }\n                            },\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 286,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                flex: 1,\n                overflow: \"auto\",\n                sx: {\n                    px: 3,\n                    py: 2\n                },\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.5\n                        },\n                        style: {\n                            height: '100%'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            alignItems: \"center\",\n                            justifyContent: \"center\",\n                            height: \"100%\",\n                            textAlign: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"h6\",\n                                    sx: {\n                                        color: 'rgba(255, 255, 255, 0.8)',\n                                        mb: 1,\n                                        fontWeight: 500\n                                    },\n                                    children: conversation ? 'Start chatting' : 'Select a conversation'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"body2\",\n                                    sx: {\n                                        color: 'rgba(255, 255, 255, 0.5)',\n                                        maxWidth: 300,\n                                        fontSize: '0.85rem'\n                                    },\n                                    children: conversation ? 'Type a message to start' : 'Select or create a conversation'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                        children: messages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: index * 0.05,\n                                    duration: 0.2\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    message: message,\n                                    isUser: message.role === 'user'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 17\n                                }, this)\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.AnimatePresence, {\n                        children: loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -20\n                            },\n                            transition: {\n                                duration: 0.3\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 317,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    p: 2,\n                    borderTop: '1px solid rgba(255, 255, 255, 0.05)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InputBar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    onSendMessage: handleSendMessage,\n                    disabled: !selectedModel || !conversation,\n                    loading: loading,\n                    modelCapabilities: modelCapabilities\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n                lineNumber: 394,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\ChatWindow.tsx\",\n        lineNumber: 283,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatWindow, \"EYjj5BViCHitUwPd0M3b8vWEXZg=\");\n_c = ChatWindow;\nvar _c;\n$RefreshReg$(_c, \"ChatWindow\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/ChatWindow/ChatWindow.tsx\n"));

/***/ })

});