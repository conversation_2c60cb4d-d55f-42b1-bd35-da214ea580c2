/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/mcp/route";
exports.ids = ["app/api/mcp/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmcp%2Froute&page=%2Fapi%2Fmcp%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmcp%2Froute&page=%2Fapi%2Fmcp%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_20021792_Documents_Projects_Chat_app_api_mcp_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/mcp/route.ts */ \"(rsc)/./app/api/mcp/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/mcp/route\",\n        pathname: \"/api/mcp\",\n        filename: \"route\",\n        bundlePath: \"app/api/mcp/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\api\\\\mcp\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_20021792_Documents_Projects_Chat_app_api_mcp_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmcp%2Froute&page=%2Fapi%2Fmcp%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./app/api/mcp/route.ts":
/*!******************************!*\
  !*** ./app/api/mcp/route.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mcpClient__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mcpClient */ \"(rsc)/./app/lib/mcpClient.ts\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\n// GET /api/mcp - Get MCP configuration and available tools\nasync function GET() {\n    try {\n        const configPath = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'public', 'mcp.config.json');\n        if (!fs__WEBPACK_IMPORTED_MODULE_2___default().existsSync(configPath)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                mcpServers: {},\n                tools: []\n            });\n        }\n        const configData = fs__WEBPACK_IMPORTED_MODULE_2___default().readFileSync(configPath, 'utf8');\n        const config = JSON.parse(configData);\n        // Start servers if not already running and get tools\n        await initializeMCPServers(config);\n        console.log('🔍 Getting available tools from MCP client...');\n        const tools = await _lib_mcpClient__WEBPACK_IMPORTED_MODULE_1__.mcpClient.getAvailableTools();\n        console.log(`🔍 Retrieved ${tools.length} tools from MCP client`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            mcpServers: config.mcpServers,\n            tools\n        });\n    } catch (error) {\n        console.error('Error reading MCP config:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to read MCP configuration'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/mcp - Execute tool call or update configuration\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { action, serverName, toolName, arguments: toolArgs, config } = body;\n        switch(action){\n            case 'execute_tool':\n                return await executeToolCall(serverName, toolName, toolArgs);\n            case 'update_config':\n                return await updateMCPConfig(config);\n            case 'start_server':\n                return await startMCPServer(serverName, config);\n            case 'stop_server':\n                return await stopMCPServer(serverName);\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid action'\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error('Error in MCP API:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to process MCP request'\n        }, {\n            status: 500\n        });\n    }\n}\n// Initialize MCP servers from config\nasync function initializeMCPServers(config) {\n    for (const [serverName, serverConfig] of Object.entries(config.mcpServers || {})){\n        if (!_lib_mcpClient__WEBPACK_IMPORTED_MODULE_1__.mcpClient.servers.has(serverName)) {\n            try {\n                console.log(`Starting MCP server: ${serverName}`);\n                await _lib_mcpClient__WEBPACK_IMPORTED_MODULE_1__.mcpClient.startServer(serverName, serverConfig);\n            } catch (error) {\n                console.error(`Failed to start MCP server ${serverName}:`, error);\n            }\n        }\n    }\n}\n// Execute a tool call using MCP client\nasync function executeToolCall(serverName, toolName, toolArgs) {\n    try {\n        console.log(`Executing tool ${toolName} on server ${serverName} with args:`, toolArgs);\n        const result = await _lib_mcpClient__WEBPACK_IMPORTED_MODULE_1__.mcpClient.executeToolCall(serverName, toolName, toolArgs);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            result: result.content || result\n        });\n    } catch (error) {\n        console.error('Error executing tool call:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Failed to execute tool call: ${error.message}`\n        }, {\n            status: 500\n        });\n    }\n}\n// Update MCP configuration\nasync function updateMCPConfig(config) {\n    try {\n        const configPath = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'public', 'mcp.config.json');\n        fs__WEBPACK_IMPORTED_MODULE_2___default().writeFileSync(configPath, JSON.stringify(config, null, 2));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        console.error('Error updating MCP config:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to update configuration'\n        }, {\n            status: 500\n        });\n    }\n}\n// Start an MCP server\nasync function startMCPServer(serverName, config) {\n    try {\n        const configPath = path__WEBPACK_IMPORTED_MODULE_3___default().join(process.cwd(), 'public', 'mcp.config.json');\n        const configData = fs__WEBPACK_IMPORTED_MODULE_2___default().readFileSync(configPath, 'utf8');\n        const mcpConfig = JSON.parse(configData);\n        const serverConfig = mcpConfig.mcpServers[serverName];\n        if (!serverConfig) {\n            throw new Error(`Server ${serverName} not found in configuration`);\n        }\n        await _lib_mcpClient__WEBPACK_IMPORTED_MODULE_1__.mcpClient.startServer(serverName, serverConfig);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `Server ${serverName} started successfully`\n        });\n    } catch (error) {\n        console.error('Error starting MCP server:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Failed to start server: ${error.message}`\n        }, {\n            status: 500\n        });\n    }\n}\n// Stop an MCP server\nasync function stopMCPServer(serverName) {\n    try {\n        await _lib_mcpClient__WEBPACK_IMPORTED_MODULE_1__.mcpClient.stopServer(serverName);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: `Server ${serverName} stopped successfully`\n        });\n    } catch (error) {\n        console.error('Error stopping MCP server:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Failed to stop server: ${error.message}`\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/mcp/route.ts\n");

/***/ }),

/***/ "(rsc)/./app/lib/mcpClient.ts":
/*!******************************!*\
  !*** ./app/lib/mcpClient.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MCPClient: () => (/* binding */ MCPClient),\n/* harmony export */   mcpClient: () => (/* binding */ mcpClient)\n/* harmony export */ });\nclass MCPClient {\n    async getAvailableTools() {\n        const allTools = [];\n        // Get tools from all connected servers\n        const serverEntries = Array.from(this.servers.entries());\n        for (const [serverName, server] of serverEntries){\n            if (server.isConnected) {\n                if (server.client) {\n                    // Real MCP client - use SDK\n                    try {\n                        const result = await server.client.listTools();\n                        const serverTools = result.tools.map((tool)=>({\n                                type: 'function',\n                                function: {\n                                    name: tool.name,\n                                    description: tool.description,\n                                    parameters: tool.inputSchema || {}\n                                },\n                                serverName: serverName\n                            }));\n                        allTools.push(...serverTools);\n                    } catch (error) {\n                        console.error(`Error getting tools from ${serverName}:`, error);\n                        // Fallback to stored tools if real connection fails\n                        allTools.push(...server.tools);\n                    }\n                } else {\n                    // Demo/Smithery server - use stored tools\n                    console.log(`Getting tools from ${serverName} (demo/Smithery mode):`, server.tools.length);\n                    allTools.push(...server.tools);\n                }\n            }\n        }\n        console.log(`Total tools available: ${allTools.length}`);\n        return allTools;\n    }\n    async executeToolCall(serverName, toolName, args) {\n        const server = this.servers.get(serverName);\n        if (!server || !server.isConnected) {\n            throw new Error(`Server ${serverName} is not connected`);\n        }\n        // Simulate tool execution for demo\n        console.log(`Executing ${toolName} on ${serverName} with args:`, args);\n        // Return demo response based on tool\n        if (toolName === 'search_web') {\n            return {\n                content: [\n                    {\n                        type: 'text',\n                        text: `Search results for \"${args.query}\": This is a demo response from the ${serverName} server.`\n                    }\n                ]\n            };\n        }\n        return {\n            content: [\n                {\n                    type: 'text',\n                    text: `Tool ${toolName} executed successfully on ${serverName} server.`\n                }\n            ]\n        };\n    }\n    async startServer(serverName, config) {\n        if (this.servers.has(serverName)) {\n            await this.stopServer(serverName);\n        }\n        try {\n            // Create demo tools based on server type\n            let demoTools = [];\n            if (serverName === 'context7') {\n                demoTools = [\n                    {\n                        type: 'function',\n                        function: {\n                            name: 'search_docs',\n                            description: 'Search through documentation and code context',\n                            parameters: {\n                                type: 'object',\n                                properties: {\n                                    query: {\n                                        type: 'string',\n                                        description: 'Search query'\n                                    },\n                                    context: {\n                                        type: 'string',\n                                        description: 'Context type (docs, code, etc.)'\n                                    }\n                                },\n                                required: [\n                                    'query'\n                                ]\n                            }\n                        },\n                        serverName\n                    }\n                ];\n            } else if (serverName === 'exa') {\n                demoTools = [\n                    {\n                        type: 'function',\n                        function: {\n                            name: 'search_web',\n                            description: 'Search the web using Exa API',\n                            parameters: {\n                                type: 'object',\n                                properties: {\n                                    query: {\n                                        type: 'string',\n                                        description: 'Search query'\n                                    },\n                                    num_results: {\n                                        type: 'number',\n                                        description: 'Number of results',\n                                        default: 10\n                                    }\n                                },\n                                required: [\n                                    'query'\n                                ]\n                            }\n                        },\n                        serverName\n                    }\n                ];\n            }\n            // Check if this is a Smithery AI server\n            console.log(`Server config for ${serverName}:`, config);\n            console.log(`Command: ${config.command}, Args:`, config.args);\n            const isSmitheryServer = config.command === 'npx' && config.args?.includes('@smithery/cli@latest') || config.command === 'cmd' && config.args?.includes('@smithery/cli@latest') || config.args?.includes('npx') && config.args?.includes('@smithery/cli@latest');\n            if (isSmitheryServer) {\n                console.log(`✅ Detected Smithery AI server: ${serverName}`);\n                // For Smithery servers, we need special handling\n                const smitheryTools = [\n                    {\n                        type: 'function',\n                        function: {\n                            name: 'search_web',\n                            description: 'Search the web using DuckDuckGo via Smithery AI',\n                            parameters: {\n                                type: 'object',\n                                properties: {\n                                    query: {\n                                        type: 'string',\n                                        description: 'Search query'\n                                    },\n                                    max_results: {\n                                        type: 'number',\n                                        description: 'Maximum number of results',\n                                        default: 5\n                                    }\n                                },\n                                required: [\n                                    'query'\n                                ]\n                            }\n                        },\n                        serverName\n                    }\n                ];\n                const serverInstance = {\n                    name: serverName,\n                    config,\n                    tools: smitheryTools,\n                    isConnected: true\n                };\n                this.servers.set(serverName, serverInstance);\n                console.log(`MCP server ${serverName} started successfully (Smithery AI)`);\n            } else {\n                // For other servers, use demo mode for now\n                const serverInstance = {\n                    name: serverName,\n                    config,\n                    tools: demoTools,\n                    isConnected: true\n                };\n                this.servers.set(serverName, serverInstance);\n                console.log(`MCP server ${serverName} started successfully (demo mode)`);\n            }\n        } catch (error) {\n            console.error(`Failed to start MCP server ${serverName}:`, error);\n            throw error;\n        }\n    }\n    async stopServer(serverName) {\n        const server = this.servers.get(serverName);\n        if (!server) {\n            return;\n        }\n        try {\n            // Close real MCP connections if they exist\n            if (server.client) {\n                await server.client.close();\n            }\n            if (server.transport) {\n                await server.transport.close();\n            }\n            if (server.process) {\n                server.process.kill();\n            }\n            this.servers.delete(serverName);\n            console.log(`MCP server ${serverName} stopped`);\n        } catch (error) {\n            console.error(`Error stopping MCP server ${serverName}:`, error);\n            this.servers.delete(serverName);\n        }\n    }\n    async stopAllServers() {\n        const stopPromises = Array.from(this.servers.keys()).map((serverName)=>this.stopServer(serverName));\n        await Promise.all(stopPromises);\n    }\n    constructor(){\n        this.servers = new Map();\n    }\n}\n// Singleton instance\nconst mcpClient = new MCPClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/lib/mcpClient.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmcp%2Froute&page=%2Fapi%2Fmcp%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();