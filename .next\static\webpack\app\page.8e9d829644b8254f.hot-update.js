"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/ChatWindow/MessageBubble.tsx":
/*!*****************************************************!*\
  !*** ./app/components/ChatWindow/MessageBubble.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MessageBubble)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,IconButton,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,IconButton,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,IconButton,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,IconButton,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,IconButton,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Check,ContentCopy,Description,Image!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Image.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Check,ContentCopy,Description,Image!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Description.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Check,ContentCopy,Description,Image!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Build.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Check,ContentCopy,Description,Image!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Check.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Check,ContentCopy,Description,Image!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ContentCopy.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction MessageBubble(param) {\n    let { message, isUser } = param;\n    _s();\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleCopyMessage = async ()=>{\n        try {\n            await navigator.clipboard.writeText(message.content);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        } catch (error) {\n            console.error('Failed to copy message:', error);\n        }\n    };\n    const formatTimestamp = (timestamp)=>{\n        return new Intl.DateTimeFormat('en-US', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: true\n        }).format(new Date(timestamp));\n    };\n    const renderAttachments = ()=>{\n        if (!message.attachments || message.attachments.length === 0) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            display: \"flex\",\n            flexWrap: \"wrap\",\n            gap: 1,\n            mb: 1,\n            children: message.attachments.map((attachment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    icon: attachment.type === 'image' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 49\n                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 65\n                    }, void 0),\n                    label: attachment.name,\n                    size: \"small\",\n                    variant: \"outlined\",\n                    onClick: ()=>window.open(attachment.url, '_blank'),\n                    sx: {\n                        cursor: 'pointer'\n                    }\n                }, attachment.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this);\n    };\n    const renderToolCall = ()=>{\n        if (!message.toolCall) {\n            return null;\n        }\n        // Handle both string and object arguments\n        let toolArgs;\n        try {\n            toolArgs = typeof message.toolCall.function.arguments === 'string' ? JSON.parse(message.toolCall.function.arguments) : message.toolCall.function.arguments;\n        } catch (parseError) {\n            console.error('Failed to parse tool arguments:', parseError);\n            // If parsing fails, display the raw arguments\n            toolArgs = message.toolCall.function.arguments;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            mb: 1,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 17\n                    }, void 0),\n                    label: \"Tool: \".concat(message.toolCall.function.name),\n                    size: \"small\",\n                    color: \"secondary\",\n                    variant: \"outlined\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    mt: 1,\n                    p: 1,\n                    bgcolor: \"grey.100\",\n                    borderRadius: 1,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            variant: \"caption\",\n                            color: \"text.secondary\",\n                            children: \"Arguments:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            variant: \"body2\",\n                            component: \"pre\",\n                            sx: {\n                                fontSize: '0.75rem'\n                            },\n                            children: typeof toolArgs === 'string' ? toolArgs : JSON.stringify(toolArgs, null, 2)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this);\n    };\n    const renderToolResult = ()=>{\n        if (!message.toolResult) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            mb: 1,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 17\n                    }, void 0),\n                    label: \"Tool Result\",\n                    size: \"small\",\n                    color: \"success\",\n                    variant: \"outlined\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    mt: 1,\n                    p: 1,\n                    bgcolor: \"success.50\",\n                    borderRadius: 1,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"body2\",\n                        component: \"pre\",\n                        sx: {\n                            fontSize: '0.875rem'\n                        },\n                        children: typeof message.toolResult.result === 'string' ? message.toolResult.result : JSON.stringify(message.toolResult.result, null, 2)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        display: \"flex\",\n        justifyContent: isUser ? 'flex-end' : 'flex-start',\n        mb: 2,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            sx: {\n                maxWidth: '80%'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                sx: {\n                    p: 2,\n                    borderRadius: 2,\n                    background: isUser ? 'rgba(255, 255, 255, 0.08)' : 'rgba(255, 255, 255, 0.03)',\n                    border: \"1px solid \".concat(isUser ? 'rgba(255, 255, 255, 0.15)' : 'rgba(255, 255, 255, 0.08)'),\n                    position: 'relative',\n                    transition: 'all 0.2s ease',\n                    '&:hover': {\n                        background: isUser ? 'rgba(255, 255, 255, 0.12)' : 'rgba(255, 255, 255, 0.06)',\n                        '& .copy-button': {\n                            opacity: 1\n                        }\n                    }\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        title: copied ? \"Copied!\" : \"Copy message\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"copy-button\",\n                            size: \"small\",\n                            onClick: handleCopyMessage,\n                            sx: {\n                                position: 'absolute',\n                                top: 6,\n                                right: 6,\n                                opacity: 0,\n                                transition: 'all 0.2s ease',\n                                color: copied ? '#4caf50' : 'rgba(255, 255, 255, 0.6)',\n                                background: 'transparent',\n                                '&:hover': {\n                                    background: 'rgba(255, 255, 255, 0.1)'\n                                }\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                initial: {\n                                    scale: 0.8,\n                                    opacity: 0\n                                },\n                                animate: {\n                                    scale: 1,\n                                    opacity: 1\n                                },\n                                transition: {\n                                    duration: 0.2\n                                },\n                                children: copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    fontSize: \"small\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 29\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    fontSize: \"small\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 62\n                                }, this)\n                            }, copied ? 'check' : 'copy', false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 13\n                    }, this),\n                    renderAttachments(),\n                    renderToolCall(),\n                    renderToolResult(),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"body1\",\n                        sx: {\n                            whiteSpace: 'pre-wrap',\n                            wordBreak: 'break-word',\n                            pr: 5,\n                            color: 'white'\n                        },\n                        children: message.content\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"caption\",\n                        sx: {\n                            display: 'block',\n                            mt: 1,\n                            color: 'rgba(255, 255, 255, 0.5)',\n                            textAlign: isUser ? 'right' : 'left',\n                            fontSize: '0.75rem'\n                        },\n                        children: formatTimestamp(message.timestamp)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                lineNumber: 150,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n            lineNumber: 145,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n        lineNumber: 140,\n        columnNumber: 5\n    }, this);\n}\n_s(MessageBubble, \"NE86rL3vg4NVcTTWDavsT0hUBJs=\");\n_c = MessageBubble;\nvar _c;\n$RefreshReg$(_c, \"MessageBubble\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/ChatWindow/MessageBubble.tsx\n"));

/***/ })

});