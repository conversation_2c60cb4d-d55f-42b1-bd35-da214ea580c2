"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/ModelSelector.tsx":
/*!******************************************!*\
  !*** ./app/components/ModelSelector.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModelSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/InputLabel/InputLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Card,CircularProgress,FormControl,InputLabel,MenuItem,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Code_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Code,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Build.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Code_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Code,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Visibility.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Code_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Code,Visibility!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Code.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ModelSelector(param) {\n    let { selectedModel, onModelSelect, onModelCapabilitiesChange } = param;\n    _s();\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDetails, setShowDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ModelSelector.useEffect\": ()=>{\n            fetchModels();\n        }\n    }[\"ModelSelector.useEffect\"], []);\n    const fetchModels = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await fetch('/api/ollama');\n            if (!response.ok) {\n                throw new Error('Failed to fetch models');\n            }\n            const data = await response.json();\n            setModels(data.models || []);\n            // Auto-select first model if none selected\n            if (!selectedModel && data.models && data.models.length > 0) {\n                const firstModel = data.models[0];\n                onModelSelect(firstModel.name);\n                onModelCapabilitiesChange === null || onModelCapabilitiesChange === void 0 ? void 0 : onModelCapabilitiesChange(firstModel.capabilities);\n            }\n        } catch (err) {\n            console.error('Error fetching models:', err);\n            setError('Failed to load Ollama models. Make sure Ollama is running.');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const formatModelSize = (size)=>{\n        const gb = size / (1024 * 1024 * 1024);\n        return \"\".concat(gb.toFixed(1), \"GB\");\n    };\n    const getModelFamily = (model)=>{\n        var _model_details;\n        if ((_model_details = model.details) === null || _model_details === void 0 ? void 0 : _model_details.family) {\n            return model.details.family;\n        }\n        return model.name.split(':')[0];\n    };\n    const getCapabilityIcon = (capability)=>{\n        switch(capability){\n            case 'tools':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Code_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 28\n                }, this);\n            case 'vision':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Code_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 29\n                }, this);\n            case 'code':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Code_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 27\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const getCapabilityColor = (capability)=>{\n        switch(capability){\n            case 'tools':\n                return 'primary';\n            case 'vision':\n                return 'secondary';\n            case 'code':\n                return 'success';\n            default:\n                return 'default';\n        }\n    };\n    const handleModelChange = (modelName)=>{\n        const model = models.find((m)=>m.name === modelName);\n        onModelSelect(modelName);\n        if (model) {\n            onModelCapabilitiesChange === null || onModelCapabilitiesChange === void 0 ? void 0 : onModelCapabilitiesChange(model.capabilities);\n        }\n    };\n    const selectedModelData = models.find((m)=>m.name === selectedModel);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            initial: {\n                opacity: 0,\n                y: 20\n            },\n            animate: {\n                opacity: 1,\n                y: 0\n            },\n            transition: {\n                duration: 0.5\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    p: 3,\n                    textAlign: 'center'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        size: 40,\n                        sx: {\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"h6\",\n                        gutterBottom: true,\n                        children: \"Discovering Models\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Connecting to Ollama and loading available models...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                lineNumber: 126,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            initial: {\n                opacity: 0,\n                scale: 0.9\n            },\n            animate: {\n                opacity: 1,\n                scale: 1\n            },\n            transition: {\n                duration: 0.5\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                severity: \"error\",\n                sx: {\n                    borderRadius: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"h6\",\n                        gutterBottom: true,\n                        children: \"Connection Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this),\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                lineNumber: 146,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n            lineNumber: 141,\n            columnNumber: 7\n        }, this);\n    }\n    if (models.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n            initial: {\n                opacity: 0,\n                scale: 0.9\n            },\n            animate: {\n                opacity: 1,\n                scale: 1\n            },\n            transition: {\n                duration: 0.5\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                severity: \"warning\",\n                sx: {\n                    borderRadius: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        variant: \"h6\",\n                        gutterBottom: true,\n                        children: \"No Models Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, this),\n                    \"No Ollama models found. Install a model using: \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        children: \"ollama pull llama3.2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 58\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                lineNumber: 163,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            fullWidth: true,\n            size: \"small\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    sx: {\n                        color: 'rgba(255, 255, 255, 0.7)'\n                    },\n                    children: \"Model\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    value: selectedModel || '',\n                    label: \"Model\",\n                    onChange: (e)=>handleModelChange(e.target.value),\n                    sx: {\n                        '& .MuiOutlinedInput-notchedOutline': {\n                            borderColor: 'rgba(255, 255, 255, 0.2)'\n                        },\n                        '&:hover .MuiOutlinedInput-notchedOutline': {\n                            borderColor: 'rgba(255, 255, 255, 0.4)'\n                        },\n                        '&.Mui-focused .MuiOutlinedInput-notchedOutline': {\n                            borderColor: 'rgba(255, 255, 255, 0.6)'\n                        },\n                        '& .MuiSelect-select': {\n                            color: 'white'\n                        },\n                        '& .MuiSvgIcon-root': {\n                            color: 'rgba(255, 255, 255, 0.7)'\n                        }\n                    },\n                    MenuProps: {\n                        PaperProps: {\n                            sx: {\n                                backdropFilter: 'blur(20px)',\n                                background: 'rgba(255, 255, 255, 0.1)',\n                                border: '1px solid rgba(255, 255, 255, 0.2)',\n                                '& .MuiMenuItem-root': {\n                                    color: 'white',\n                                    '&:hover': {\n                                        backgroundColor: 'rgba(255, 255, 255, 0.1)'\n                                    },\n                                    '&.Mui-selected': {\n                                        backgroundColor: 'rgba(255, 255, 255, 0.2)',\n                                        '&:hover': {\n                                            backgroundColor: 'rgba(255, 255, 255, 0.25)'\n                                        }\n                                    }\n                                }\n                            }\n                        }\n                    },\n                    renderValue: (value)=>{\n                        const model = models.find((m)=>m.name === value);\n                        if (!model) return value;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            gap: 1,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    variant: \"body2\",\n                                    sx: {\n                                        flex: 1\n                                    },\n                                    children: model.name\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 17\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    display: \"flex\",\n                                    gap: 0.5,\n                                    children: [\n                                        model.capabilities.supportsTools && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            sx: {\n                                                width: 6,\n                                                height: 6,\n                                                borderRadius: '50%',\n                                                bgcolor: 'white',\n                                                opacity: 0.7\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        model.capabilities.supportsVision && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            sx: {\n                                                width: 6,\n                                                height: 6,\n                                                borderRadius: '50%',\n                                                bgcolor: 'white',\n                                                opacity: 0.5\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        model.capabilities.supportsCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            sx: {\n                                                width: 6,\n                                                height: 6,\n                                                borderRadius: '50%',\n                                                bgcolor: 'white',\n                                                opacity: 0.3\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 17\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 15\n                        }, void 0);\n                    },\n                    children: models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            value: model.name,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"space-between\",\n                                width: \"100%\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                variant: \"body2\",\n                                                fontWeight: 500,\n                                                children: model.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                variant: \"caption\",\n                                                color: \"text.secondary\",\n                                                children: [\n                                                    formatModelSize(model.size),\n                                                    \" • \",\n                                                    getModelFamily(model)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        display: \"flex\",\n                                        gap: 0.5,\n                                        children: [\n                                            model.capabilities.supportsTools && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                title: \"Tools\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    sx: {\n                                                        width: 8,\n                                                        height: 8,\n                                                        borderRadius: '50%',\n                                                        bgcolor: 'white',\n                                                        opacity: 0.8\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 23\n                                            }, this),\n                                            model.capabilities.supportsVision && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                title: \"Vision\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    sx: {\n                                                        width: 8,\n                                                        height: 8,\n                                                        borderRadius: '50%',\n                                                        bgcolor: 'white',\n                                                        opacity: 0.6\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 23\n                                            }, this),\n                                            model.capabilities.supportsCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                title: \"Code\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    sx: {\n                                                        width: 8,\n                                                        height: 8,\n                                                        borderRadius: '50%',\n                                                        bgcolor: 'white',\n                                                        opacity: 0.4\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 23\n                                            }, this),\n                                            model.capabilities.thinking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                title: \"Thinking\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Card_CircularProgress_FormControl_InputLabel_MenuItem_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    sx: {\n                                                        width: 8,\n                                                        height: 8,\n                                                        borderRadius: '50%',\n                                                        bgcolor: 'white',\n                                                        opacity: 0.9\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 17\n                            }, this)\n                        }, model.name, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n            lineNumber: 175,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ModelSelector.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, this);\n}\n_s(ModelSelector, \"TjFVH2cxAcN3Y8lHn1OogUh0Rkc=\");\n_c = ModelSelector;\nvar _c;\n$RefreshReg$(_c, \"ModelSelector\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/ModelSelector.tsx\n"));

/***/ })

});