import { NextRequest, NextResponse } from 'next/server';
import { MCPConfig, Tool } from '@/types';
import { mcpClient } from '@/lib/mcpClient';
import fs from 'fs';
import path from 'path';

// GET /api/mcp - Get MCP configuration and available tools
export async function GET() {
  try {
    const configPath = path.join(process.cwd(), 'public', 'mcp.config.json');

    if (!fs.existsSync(configPath)) {
      return NextResponse.json({ mcpServers: {}, tools: [] });
    }

    const configData = fs.readFileSync(configPath, 'utf8');
    const config: MCPConfig = JSON.parse(configData);

    // Start servers if not already running and get tools
    await initializeMCPServers(config);
    console.log('🔍 Getting available tools from MCP client...');
    const tools: Tool[] = await mcpClient.getAvailableTools();
    console.log(`🔍 Retrieved ${tools.length} tools from MCP client`);

    return NextResponse.json({
      mcpServers: config.mcpServers,
      tools,
    });
  } catch (error) {
    console.error('Error reading MCP config:', error);
    return NextResponse.json(
      { error: 'Failed to read MCP configuration' },
      { status: 500 }
    );
  }
}

// POST /api/mcp - Execute tool call or update configuration
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, serverName, toolName, arguments: toolArgs, config } = body;

    switch (action) {
      case 'execute_tool':
        return await executeToolCall(serverName, toolName, toolArgs);

      case 'update_config':
        return await updateMCPConfig(config);

      case 'start_server':
        return await startMCPServer(serverName, config);

      case 'stop_server':
        return await stopMCPServer(serverName);

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error in MCP API:', error);
    return NextResponse.json(
      { error: 'Failed to process MCP request' },
      { status: 500 }
    );
  }
}

// Initialize MCP servers from config
async function initializeMCPServers(config: MCPConfig) {
  for (const [serverName, serverConfig] of Object.entries(config.mcpServers || {})) {
    if (!mcpClient.servers.has(serverName)) {
      try {
        console.log(`Starting MCP server: ${serverName}`);
        await mcpClient.startServer(serverName, serverConfig);
      } catch (error) {
        console.error(`Failed to start MCP server ${serverName}:`, error);
      }
    }
  }
}

// Execute a tool call using MCP client
async function executeToolCall(serverName: string, toolName: string, toolArgs: any) {
  try {
    console.log(`Executing tool ${toolName} on server ${serverName} with args:`, toolArgs);

    const result = await mcpClient.executeToolCall(serverName, toolName, toolArgs);

    return NextResponse.json({
      success: true,
      result: result.content || result,
    });
  } catch (error) {
    console.error('Error executing tool call:', error);
    return NextResponse.json(
      { error: `Failed to execute tool call: ${error.message}` },
      { status: 500 }
    );
  }
}

// Update MCP configuration
async function updateMCPConfig(config: MCPConfig) {
  try {
    const configPath = path.join(process.cwd(), 'public', 'mcp.config.json');
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error updating MCP config:', error);
    return NextResponse.json(
      { error: 'Failed to update configuration' },
      { status: 500 }
    );
  }
}

// Start an MCP server
async function startMCPServer(serverName: string, config: any) {
  try {
    const configPath = path.join(process.cwd(), 'public', 'mcp.config.json');
    const configData = fs.readFileSync(configPath, 'utf8');
    const mcpConfig: MCPConfig = JSON.parse(configData);

    const serverConfig = mcpConfig.mcpServers[serverName];
    if (!serverConfig) {
      throw new Error(`Server ${serverName} not found in configuration`);
    }

    await mcpClient.startServer(serverName, serverConfig);

    return NextResponse.json({
      success: true,
      message: `Server ${serverName} started successfully`
    });
  } catch (error) {
    console.error('Error starting MCP server:', error);
    return NextResponse.json(
      { error: `Failed to start server: ${error.message}` },
      { status: 500 }
    );
  }
}

// Stop an MCP server
async function stopMCPServer(serverName: string) {
  try {
    await mcpClient.stopServer(serverName);

    return NextResponse.json({
      success: true,
      message: `Server ${serverName} stopped successfully`
    });
  } catch (error) {
    console.error('Error stopping MCP server:', error);
    return NextResponse.json(
      { error: `Failed to stop server: ${error.message}` },
      { status: 500 }
    );
  }
}
