/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ollama/route";
exports.ids = ["app/api/ollama/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Follama%2Froute&page=%2Fapi%2Follama%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Follama%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Follama%2Froute&page=%2Fapi%2Follama%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Follama%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_20021792_Documents_Projects_Chat_app_api_ollama_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/ollama/route.ts */ \"(rsc)/./app/api/ollama/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ollama/route\",\n        pathname: \"/api/ollama\",\n        filename: \"route\",\n        bundlePath: \"app/api/ollama/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\api\\\\ollama\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_20021792_Documents_Projects_Chat_app_api_ollama_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Follama%2Froute&page=%2Fapi%2Follama%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Follama%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./app/api/ollama/route.ts":
/*!*********************************!*\
  !*** ./app/api/ollama/route.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nconst OLLAMA_BASE_URL = \"http://localhost:11434\" || 0;\n// Function to detect model capabilities based on real Ollama model data\nfunction detectModelCapabilities(model) {\n    const modelName = model.name.toLowerCase();\n    const family = model.details?.family?.toLowerCase() || '';\n    const template = model.details?.template?.toLowerCase() || '';\n    const parameters = model.details?.parameters || '';\n    console.log(`🔍 Analyzing capabilities for ${modelName}:`);\n    console.log(`- Family: ${family}`);\n    console.log(`- Template length: ${template.length} chars`);\n    console.log(`- Parameters: ${parameters}`);\n    console.log(`- Model details:`, model.details);\n    // ACCURATE tool detection - only models that actually support function calling\n    const supportsTools = // Check template for actual tool/function calling patterns\n    template.includes('function_call') || template.includes('tool_call') || template.includes('available_functions') || template.includes('function>') || template.includes('<tool>') || template.includes('tools:') || template.includes('call_function') || // Check for specific tool-capable models (VERIFIED ONLY)\n    modelName.includes('llama3.1') || modelName.includes('llama3.2') || modelName.includes('llama3.3') || modelName.includes('qwen2.5') || modelName.includes('qwen2') || modelName.includes('qwen3') || modelName.includes('mistral-nemo') || modelName.includes('mistral') && !modelName.includes('7b') || modelName.includes('firefunction') || modelName.includes('hermes-3') || modelName.includes('nous-hermes') || modelName.includes('deepseek-v3') || modelName.includes('granite3') || // Check parameters for function calling indicators\n    parameters.includes('function_call') || parameters.includes('tool_call');\n    // ACCURATE vision detection - multimodal models that can process images\n    const supportsVision = // Check template for vision/image patterns\n    template.includes('<image>') || template.includes('[img]') || template.includes('image_url') || template.includes('vision') || template.includes('multimodal') || template.includes('image') || // Check for specific vision models (VERIFIED LIST)\n    modelName.includes('llava') || modelName.includes('moondream') || modelName.includes('minicpm-v') || modelName.includes('qwen2-vl') || modelName.includes('qwen-vl') || modelName.includes('pixtral') || modelName.includes('internvl') || modelName.includes('cogvlm') || modelName.includes('yi-vl') || modelName.includes('gemma2-2b-it') || modelName.includes('gemma2-9b-it') || modelName.includes('gemma2-27b-it') || modelName.includes('gemma3-4b') || modelName.includes('gemma3-8b') || modelName.includes('llama3.2-11b-vision') || modelName.includes('llama3.2-90b-vision') || // Check family for vision indicators\n    family.includes('llava') || family.includes('vision') || family.includes('gemma2') || family.includes('gemma3');\n    // ACCURATE code detection\n    const supportsCode = // Check for specific code models\n    modelName.includes('codellama') || modelName.includes('codegemma') || modelName.includes('starcoder') || modelName.includes('deepseek-coder') || modelName.includes('codeqwen') || modelName.includes('granite-code') || family.includes('code');\n    // ACCURATE thinking detection - models with reasoning capabilities\n    const thinking = // Check template for thinking patterns\n    template.includes('<think>') || template.includes('<thinking>') || template.includes('reasoning') || // Check for specific reasoning models\n    modelName.includes('qwen2.5') || modelName.includes('qwen3') || modelName.includes('deepseek-r1') || modelName.includes('deepseek-v3') || modelName.includes('o1') || modelName.includes('reasoning') || // Check parameters\n    parameters.includes('thinking') || parameters.includes('reasoning');\n    console.log(`✅ Capabilities: Tools=${supportsTools}, Vision=${supportsVision}, Code=${supportsCode}, Thinking=${thinking}`);\n    return {\n        supportsTools,\n        supportsVision,\n        supportsCode,\n        thinking,\n        contextLength: getContextLength(modelName, family)\n    };\n}\nfunction getContextLength(modelName, family) {\n    // Common context lengths based on model families\n    if (modelName.includes('128k') || family.includes('granite')) return 128000;\n    if (modelName.includes('32k')) return 32000;\n    if (modelName.includes('16k')) return 16000;\n    if (modelName.includes('8k')) return 8000;\n    if (family.includes('llama3')) return 8192;\n    if (family.includes('qwen')) return 32768;\n    if (family.includes('mistral')) return 32768;\n    return 4096; // Default\n}\n// GET /api/ollama - Fetch available models with detailed capabilities\nasync function GET() {\n    try {\n        const response = await fetch(`${OLLAMA_BASE_URL}/api/tags`);\n        if (!response.ok) {\n            throw new Error(`Ollama API error: ${response.status}`);\n        }\n        const data = await response.json();\n        // Fetch detailed model information for each model\n        const modelsWithDetails = await Promise.all((data.models || []).map(async (model)=>{\n            try {\n                // Get detailed model information\n                const showResponse = await fetch(`${OLLAMA_BASE_URL}/api/show`, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        name: model.name\n                    })\n                });\n                if (showResponse.ok) {\n                    const modelDetails = await showResponse.json();\n                    return {\n                        ...model,\n                        details: {\n                            ...model.details,\n                            ...modelDetails.details,\n                            template: modelDetails.template,\n                            parameters: modelDetails.parameters,\n                            modelfile: modelDetails.modelfile\n                        },\n                        capabilities: detectModelCapabilities({\n                            ...model,\n                            details: modelDetails.details,\n                            template: modelDetails.template,\n                            parameters: modelDetails.parameters\n                        })\n                    };\n                }\n            } catch (error) {\n                console.error(`Error fetching details for ${model.name}:`, error);\n            }\n            // Fallback to basic detection if detailed fetch fails\n            return {\n                ...model,\n                capabilities: detectModelCapabilities(model)\n            };\n        }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            models: modelsWithDetails\n        });\n    } catch (error) {\n        console.error('Error fetching Ollama models:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch models from Ollama'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/ollama - Send chat message\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { model, messages, tools, hasVisionContent } = body;\n        if (!model) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Model is required'\n            }, {\n                status: 400\n            });\n        }\n        if (!messages || !Array.isArray(messages)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Messages array is required'\n            }, {\n                status: 400\n            });\n        }\n        // Format messages for Ollama API\n        const formattedMessages = messages.map((msg)=>({\n                role: msg.role,\n                content: msg.content,\n                ...msg.toolCall && {\n                    tool_calls: [\n                        msg.toolCall\n                    ]\n                }\n            }));\n        // Get model capabilities to determine what features to include\n        const modelsResponse = await fetch(`${OLLAMA_BASE_URL}/api/tags`);\n        let modelCapabilities = {\n            supportsTools: false,\n            supportsVision: false,\n            supportsCode: false,\n            thinking: false\n        };\n        if (modelsResponse.ok) {\n            const modelsData = await modelsResponse.json();\n            const modelInfo = modelsData.models?.find((m)=>m.name === model);\n            if (modelInfo) {\n                modelCapabilities = detectModelCapabilities(modelInfo);\n            }\n        }\n        // Prepare the request payload\n        const payload = {\n            model,\n            messages: formattedMessages,\n            stream: false\n        };\n        // Only include tools if the model supports them and tools are provided\n        if (tools && tools.length > 0 && modelCapabilities.supportsTools) {\n            payload.tools = tools;\n            console.log(`Sending tools to ${model}:`, JSON.stringify(tools, null, 2));\n            console.log(`Model capabilities:`, modelCapabilities);\n        } else {\n            console.log(`Not sending tools - Model: ${model}, Tools available: ${tools?.length || 0}, Supports tools: ${modelCapabilities.supportsTools}`);\n        }\n        // Validate vision content for non-vision models\n        if (hasVisionContent && !modelCapabilities.supportsVision) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Model ${model} does not support vision/image processing. Please use a vision-capable model like llava or qwen2-vl.`\n            }, {\n                status: 400\n            });\n        }\n        // Make request to Ollama\n        const response = await fetch(`${OLLAMA_BASE_URL}/api/chat`, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(payload)\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error('Ollama API error:', errorText);\n            throw new Error(`Ollama API error: ${response.status}`);\n        }\n        const data = await response.json();\n        // Log the response for debugging\n        console.log('Ollama response:', JSON.stringify(data, null, 2));\n        // Check if the model made tool calls\n        if (data.message.tool_calls && data.message.tool_calls.length > 0) {\n            console.log('Tool calls detected:', data.message.tool_calls);\n        }\n        // Return the response\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: data.message,\n            model: data.model,\n            created_at: data.created_at,\n            done: data.done\n        });\n    } catch (error) {\n        console.error('Error in Ollama chat:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to process chat request'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/ollama/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Follama%2Froute&page=%2Fapi%2Follama%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Follama%2Froute.ts&appDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5C20021792%5CDocuments%5CProjects%5CChat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();