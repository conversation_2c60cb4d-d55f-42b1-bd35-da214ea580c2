'use client';

import React, { useState } from 'react';
import {
  Box,
  TextField,
  Stack,
  useTheme,
  alpha,
  IconButton,
  Typography,
} from '@mui/material';
import {
  Send as SendIcon,
  Rocket as RocketIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { FileAttachment as FileAttachmentType } from '@/types';
import FileAttachment from '../FileAttachment';

interface InputBarProps {
  onSendMessage: (content: string, attachments?: FileAttachmentType[]) => void;
  disabled?: boolean;
  loading?: boolean;
  modelCapabilities?: any;
}

export default function InputBar({ onSendMessage, disabled, loading, modelCapabilities }: InputBarProps) {
  const [message, setMessage] = useState('');
  const [attachedFiles, setAttachedFiles] = useState<File[]>([]);
  const theme = useTheme();

  const handleSend = async () => {
    if (message.trim() || attachedFiles.length > 0) {
      // Convert files to FileAttachment format
      const attachments: FileAttachmentType[] = await Promise.all(
        attachedFiles.map(async (file) => ({
          name: file.name,
          type: file.type,
          size: file.size,
          data: await fileToBase64(file),
        }))
      );

      onSendMessage(message.trim(), attachments);
      setMessage('');
      setAttachedFiles([]);
    }
  };

  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const canSend = (message.trim() || attachedFiles.length > 0) && !disabled && !loading;

  return (
    <Box>
      <Stack spacing={1.5}>
        {/* Attached Files Display */}
        {attachedFiles.length > 0 && (
          <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
            {attachedFiles.map((file, index) => (
              <Box
                key={`${file.name}-${index}`}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5,
                  px: 1,
                  py: 0.5,
                  borderRadius: 1,
                  background: 'rgba(255, 255, 255, 0.05)',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                  fontSize: '0.75rem',
                }}
              >
                <Typography variant="caption" sx={{ fontSize: '0.7rem' }}>
                  {file.name.length > 20 ? `${file.name.substring(0, 20)}...` : file.name}
                </Typography>
                <IconButton
                  onClick={() => setAttachedFiles(prev => prev.filter((_, i) => i !== index))}
                  sx={{
                    p: 0.25,
                    ml: 0.5,
                    color: 'rgba(255, 255, 255, 0.5)',
                    '&:hover': {
                      color: '#ff6b6b',
                      background: 'rgba(255, 107, 107, 0.1)',
                    },
                  }}
                >
                  <CloseIcon sx={{ fontSize: '0.8rem' }} />
                </IconButton>
              </Box>
            ))}
          </Box>
        )}

        {/* Input Field with Inline Controls */}
          <TextField
            fullWidth
            multiline
            maxRows={4}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message..."
            disabled={disabled}
            variant="outlined"
            InputProps={{
              startAdornment: (
                <FileAttachment
                  onFilesSelected={(files) => setAttachedFiles(prev => [...prev, ...files])}
                  attachedFiles={[]}
                  onRemoveFile={() => {}}
                  disabled={disabled || loading}
                />
              ),
              endAdornment: (
                <IconButton
                  onClick={handleSend}
                  disabled={!canSend}
                  sx={{
                    p: 0.75,
                    mr: 0.5,
                    background: canSend
                      ? 'rgba(255, 255, 255, 0.9)'
                      : 'rgba(255, 255, 255, 0.1)',
                    color: canSend ? '#000' : 'rgba(255, 255, 255, 0.3)',
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      background: canSend
                        ? 'rgba(255, 255, 255, 1)'
                        : 'rgba(255, 255, 255, 0.1)',
                      transform: canSend ? 'scale(1.05)' : 'none',
                    },
                    '&:disabled': {
                      color: 'rgba(255, 255, 255, 0.2)',
                    },
                  }}
                >
                  {loading ? <RocketIcon /> : <SendIcon />}
                </IconButton>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
                background: 'rgba(255, 255, 255, 0.03)',
                border: `1px solid rgba(255, 255, 255, 0.1)`,
                transition: 'all 0.2s ease',
                paddingLeft: '8px',
                '&:hover': {
                  border: `1px solid rgba(255, 255, 255, 0.2)`,
                },
                '&.Mui-focused': {
                  border: `1px solid rgba(255, 255, 255, 0.3)`,
                },
              },
              '& .MuiInputBase-input': {
                fontSize: '0.9rem',
                lineHeight: 1.4,
                paddingLeft: '8px',
              },
              '& .MuiInputAdornment-root': {
                marginRight: '4px',
              },
            }}
          />
        </Stack>
      </Box>
    );
  }
