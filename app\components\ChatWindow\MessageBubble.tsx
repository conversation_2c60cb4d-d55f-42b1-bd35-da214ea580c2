'use client';

import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Avatar,
  Chip,
  IconButton,
  Tooltip,
  Fade,
} from '@mui/material';
import {
  Person as PersonIcon,
  SmartToy as BotIcon,
  Build as ToolIcon,
  Image as ImageIcon,
  Description as DocumentIcon,
  ContentCopy as CopyIcon,
  Check as CheckIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { ChatMessage } from '@/types';

interface MessageBubbleProps {
  message: ChatMessage;
  isUser: boolean;
}

export default function MessageBubble({ message, isUser }: MessageBubbleProps) {
  const [copied, setCopied] = useState(false);

  const handleCopyMessage = async () => {
    try {
      await navigator.clipboard.writeText(message.content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy message:', error);
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    }).format(new Date(timestamp));
  };

  const renderAttachments = () => {
    if (!message.attachments || message.attachments.length === 0) {
      return null;
    }

    return (
      <Box display="flex" flexWrap="wrap" gap={1} mb={1}>
        {message.attachments.map((attachment) => (
          <Chip
            key={attachment.id}
            icon={attachment.type === 'image' ? <ImageIcon /> : <DocumentIcon />}
            label={attachment.name}
            size="small"
            variant="outlined"
            onClick={() => window.open(attachment.url, '_blank')}
            sx={{ cursor: 'pointer' }}
          />
        ))}
      </Box>
    );
  };

  const renderToolCall = () => {
    if (!message.toolCall) {
      return null;
    }

    const toolArgs = JSON.parse(message.toolCall.function.arguments);

    return (
      <Box mb={1}>
        <Chip
          icon={<ToolIcon />}
          label={`Tool: ${message.toolCall.function.name}`}
          size="small"
          color="secondary"
          variant="outlined"
        />
        <Box mt={1} p={1} bgcolor="grey.100" borderRadius={1}>
          <Typography variant="caption" color="text.secondary">
            Arguments:
          </Typography>
          <Typography variant="body2" component="pre" sx={{ fontSize: '0.75rem' }}>
            {JSON.stringify(toolArgs, null, 2)}
          </Typography>
        </Box>
      </Box>
    );
  };

  const renderToolResult = () => {
    if (!message.toolResult) {
      return null;
    }

    return (
      <Box mb={1}>
        <Chip
          icon={<ToolIcon />}
          label="Tool Result"
          size="small"
          color="success"
          variant="outlined"
        />
        <Box mt={1} p={1} bgcolor="success.50" borderRadius={1}>
          <Typography variant="body2" component="pre" sx={{ fontSize: '0.875rem' }}>
            {typeof message.toolResult.result === 'string'
              ? message.toolResult.result
              : JSON.stringify(message.toolResult.result, null, 2)}
          </Typography>
        </Box>
      </Box>
    );
  };

  return (
    <Box
      display="flex"
      justifyContent={isUser ? 'flex-end' : 'flex-start'}
      mb={2}
    >
        <Box
          sx={{
            maxWidth: '80%',
          }}
        >
          <Box
            sx={{
              p: 2,
              borderRadius: 2,
              background: isUser
                ? 'rgba(255, 255, 255, 0.08)'
                : 'rgba(255, 255, 255, 0.03)',
              border: `1px solid ${isUser ? 'rgba(255, 255, 255, 0.15)' : 'rgba(255, 255, 255, 0.08)'}`,
              position: 'relative',
              transition: 'all 0.2s ease',
              '&:hover': {
                background: isUser
                  ? 'rgba(255, 255, 255, 0.12)'
                  : 'rgba(255, 255, 255, 0.06)',
                '& .copy-button': {
                  opacity: 1,
                },
              },
            }}
          >
            {/* Copy button */}
            <Tooltip title={copied ? "Copied!" : "Copy message"}>
              <IconButton
                className="copy-button"
                size="small"
                onClick={handleCopyMessage}
                sx={{
                  position: 'absolute',
                  top: 6,
                  right: 6,
                  opacity: 0,
                  transition: 'all 0.2s ease',
                  color: copied ? '#4caf50' : 'rgba(255, 255, 255, 0.6)',
                  background: 'transparent',
                  '&:hover': {
                    background: 'rgba(255, 255, 255, 0.1)',
                  },
                }}
              >
                <motion.div
                  key={copied ? 'check' : 'copy'}
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  {copied ? <CheckIcon fontSize="small" /> : <CopyIcon fontSize="small" />}
                </motion.div>
              </IconButton>
            </Tooltip>

        {/* Attachments */}
        {renderAttachments()}

        {/* Tool Call */}
        {renderToolCall()}

        {/* Tool Result */}
        {renderToolResult()}

        {/* Message Content */}
        <Typography
          variant="body1"
          sx={{
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word',
            pr: 5, // Space for copy button
            color: 'white',
          }}
        >
          {message.content}
        </Typography>

            {/* Timestamp */}
            <Typography
              variant="caption"
              sx={{
                display: 'block',
                mt: 1,
                color: 'rgba(255, 255, 255, 0.5)',
                textAlign: isUser ? 'right' : 'left',
                fontSize: '0.75rem',
              }}
            >
              {formatTimestamp(message.timestamp)}
            </Typography>
          </Box>
        </Box>
      </Box>
  );
}
