"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/components/ChatWindow/MessageBubble.tsx":
/*!*****************************************************!*\
  !*** ./app/components/ChatWindow/MessageBubble.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MessageBubble)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,IconButton,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,IconButton,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,IconButton,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,IconButton,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,IconButton,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/./node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Check,ContentCopy,Description,Image!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Image.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Check,ContentCopy,Description,Image!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Description.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Check,ContentCopy,Description,Image!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Build.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Check,ContentCopy,Description,Image!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/Check.js\");\n/* harmony import */ var _barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Build,Check,ContentCopy,Description,Image!=!@mui/icons-material */ \"(app-pages-browser)/./node_modules/@mui/icons-material/esm/ContentCopy.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction MessageBubble(param) {\n    let { message, isUser } = param;\n    _s();\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleCopyMessage = async ()=>{\n        try {\n            await navigator.clipboard.writeText(message.content);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        } catch (error) {\n            console.error('Failed to copy message:', error);\n        }\n    };\n    const formatTimestamp = (timestamp)=>{\n        return new Intl.DateTimeFormat('en-US', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: true\n        }).format(new Date(timestamp));\n    };\n    const renderAttachments = ()=>{\n        if (!message.attachments || message.attachments.length === 0) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            display: \"flex\",\n            flexWrap: \"wrap\",\n            gap: 1,\n            mb: 1,\n            children: message.attachments.map((attachment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    icon: attachment.type === 'image' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 49\n                    }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 65\n                    }, void 0),\n                    label: attachment.name,\n                    size: \"small\",\n                    variant: \"outlined\",\n                    onClick: ()=>window.open(attachment.url, '_blank'),\n                    sx: {\n                        cursor: 'pointer'\n                    }\n                }, attachment.id, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this);\n    };\n    const renderToolCall = ()=>{\n        if (!message.toolCall) {\n            return null;\n        }\n        // Handle both string and object arguments\n        let toolArgs;\n        try {\n            toolArgs = typeof message.toolCall.function.arguments === 'string' ? JSON.parse(message.toolCall.function.arguments) : message.toolCall.function.arguments;\n        } catch (parseError) {\n            console.error('Failed to parse tool arguments:', parseError);\n            // If parsing fails, display the raw arguments\n            toolArgs = message.toolCall.function.arguments;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            mb: 1,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 17\n                    }, void 0),\n                    label: \"Tool: \".concat(message.toolCall.function.name),\n                    size: \"small\",\n                    color: \"secondary\",\n                    variant: \"outlined\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    mt: 1,\n                    p: 1,\n                    bgcolor: \"grey.100\",\n                    borderRadius: 1,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            variant: \"caption\",\n                            color: \"text.secondary\",\n                            children: \"Arguments:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            variant: \"body2\",\n                            component: \"pre\",\n                            sx: {\n                                fontSize: '0.75rem'\n                            },\n                            children: JSON.stringify(toolArgs, null, 2)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this);\n    };\n    const renderToolResult = ()=>{\n        if (!message.toolResult) {\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            mb: 1,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 17\n                    }, void 0),\n                    label: \"Tool Result\",\n                    size: \"small\",\n                    color: \"success\",\n                    variant: \"outlined\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    mt: 1,\n                    p: 1,\n                    bgcolor: \"success.50\",\n                    borderRadius: 1,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"body2\",\n                        component: \"pre\",\n                        sx: {\n                            fontSize: '0.875rem'\n                        },\n                        children: typeof message.toolResult.result === 'string' ? message.toolResult.result : JSON.stringify(message.toolResult.result, null, 2)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n            lineNumber: 118,\n            columnNumber: 7\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        display: \"flex\",\n        justifyContent: isUser ? 'flex-end' : 'flex-start',\n        mb: 2,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            sx: {\n                maxWidth: '80%'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                sx: {\n                    p: 2,\n                    borderRadius: 2,\n                    background: isUser ? 'rgba(255, 255, 255, 0.08)' : 'rgba(255, 255, 255, 0.03)',\n                    border: \"1px solid \".concat(isUser ? 'rgba(255, 255, 255, 0.15)' : 'rgba(255, 255, 255, 0.08)'),\n                    position: 'relative',\n                    transition: 'all 0.2s ease',\n                    '&:hover': {\n                        background: isUser ? 'rgba(255, 255, 255, 0.12)' : 'rgba(255, 255, 255, 0.06)',\n                        '& .copy-button': {\n                            opacity: 1\n                        }\n                    }\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        title: copied ? \"Copied!\" : \"Copy message\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"copy-button\",\n                            size: \"small\",\n                            onClick: handleCopyMessage,\n                            sx: {\n                                position: 'absolute',\n                                top: 6,\n                                right: 6,\n                                opacity: 0,\n                                transition: 'all 0.2s ease',\n                                color: copied ? '#4caf50' : 'rgba(255, 255, 255, 0.6)',\n                                background: 'transparent',\n                                '&:hover': {\n                                    background: 'rgba(255, 255, 255, 0.1)'\n                                }\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.motion.div, {\n                                initial: {\n                                    scale: 0.8,\n                                    opacity: 0\n                                },\n                                animate: {\n                                    scale: 1,\n                                    opacity: 1\n                                },\n                                transition: {\n                                    duration: 0.2\n                                },\n                                children: copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    fontSize: \"small\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 29\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Build_Check_ContentCopy_Description_Image_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    fontSize: \"small\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 62\n                                }, this)\n                            }, copied ? 'check' : 'copy', false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 13\n                    }, this),\n                    renderAttachments(),\n                    renderToolCall(),\n                    renderToolResult(),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"body1\",\n                        sx: {\n                            whiteSpace: 'pre-wrap',\n                            wordBreak: 'break-word',\n                            pr: 5,\n                            color: 'white'\n                        },\n                        children: message.content\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_IconButton_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"caption\",\n                        sx: {\n                            display: 'block',\n                            mt: 1,\n                            color: 'rgba(255, 255, 255, 0.5)',\n                            textAlign: isUser ? 'right' : 'left',\n                            fontSize: '0.75rem'\n                        },\n                        children: formatTimestamp(message.timestamp)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n                lineNumber: 148,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n            lineNumber: 143,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\Chat\\\\app\\\\components\\\\ChatWindow\\\\MessageBubble.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, this);\n}\n_s(MessageBubble, \"NE86rL3vg4NVcTTWDavsT0hUBJs=\");\n_c = MessageBubble;\nvar _c;\n$RefreshReg$(_c, \"MessageBubble\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/ChatWindow/MessageBubble.tsx\n"));

/***/ })

});